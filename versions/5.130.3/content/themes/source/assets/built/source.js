function dropdown(){const r=window.matchMedia("(max-width: 767px)"),a=document.querySelector(".gh-navigation"),s=a.querySelector(".gh-navigation-menu"),l=s?.querySelector(".nav");if(l){const e=document.querySelector(".gh-navigation-logo"),t=l.innerHTML;r.matches&&l.querySelectorAll("li").forEach(function(e,t){e.style.transitionDelay=.03*(t+1)+"s"});const n=function(){if(!r.matches){for(var e=[];l.offsetWidth+64>s.offsetWidth&&l.lastElementChild;)e.unshift(l.lastElementChild),l.lastElementChild.remove();if(e.length){const o=document.createElement("button"),i=(o.setAttribute("class","gh-more-toggle gh-icon-button"),o.setAttribute("aria-label","More"),o.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor"><path d="M21.333 16c0-1.473 1.194-2.667 2.667-2.667v0c1.473 0 2.667 1.194 2.667 2.667v0c0 1.473-1.194 2.667-2.667 2.667v0c-1.473 0-2.667-1.194-2.667-2.667v0zM13.333 16c0-1.473 1.194-2.667 2.667-2.667v0c1.473 0 2.667 1.194 2.667 2.667v0c0 1.473-1.194 2.667-2.667 2.667v0c-1.473 0-2.667-1.194-2.667-2.667v0zM5.333 16c0-1.473 1.194-2.667 2.667-2.667v0c1.473 0 2.667 1.194 2.667 2.667v0c0 1.473-1.194 2.667-2.667 2.667v0c-1.473 0-2.667-1.194-2.667-2.667v0z"></path></svg>',document.createElement("div"));i.setAttribute("class","gh-dropdown"),10<=e.length?(a.classList.add("is-dropdown-mega"),i.style.gridTemplateRows=`repeat(${Math.ceil(e.length/2)}, 1fr)`):a.classList.remove("is-dropdown-mega"),e.forEach(function(e){i.appendChild(e)}),o.appendChild(i),l.appendChild(o);var t=o.getBoundingClientRect(),n=window.innerWidth/2;t.left<n&&i.classList.add("is-left"),a.classList.add("is-dropdown-loaded"),window.addEventListener("click",function(e){a.classList.contains("is-dropdown-open")?a.classList.remove("is-dropdown-open"):o.contains(e.target)&&a.classList.add("is-dropdown-open")})}else a.classList.add("is-dropdown-loaded")}};imagesLoaded(e,function(){n()}),window.addEventListener("load",function(){e||n()}),window.addEventListener("resize",function(){setTimeout(()=>{l.innerHTML=t,n()},1)})}}function lightbox(e){document.querySelectorAll(e).forEach(function(e){e.addEventListener("click",function(e){var t=e;t.preventDefault();for(var n,o=[],i=0,r=t.target.closest(".kg-card").previousElementSibling;r&&(r.classList.contains("kg-image-card")||r.classList.contains("kg-gallery-card"));){var a=[];r.querySelectorAll("img").forEach(function(e){a.push({src:e.getAttribute("src"),msrc:e.getAttribute("src"),w:e.getAttribute("width"),h:e.getAttribute("height"),el:e}),i+=1}),r=r.previousElementSibling,o=a.concat(o)}t.target.classList.contains("kg-image")?o.push({src:t.target.getAttribute("src"),msrc:t.target.getAttribute("src"),w:t.target.getAttribute("width"),h:t.target.getAttribute("height"),el:t.target}):(n=!1,t.target.closest(".kg-gallery-card").querySelectorAll("img").forEach(function(e){o.push({src:e.getAttribute("src"),msrc:e.getAttribute("src"),w:e.getAttribute("width"),h:e.getAttribute("height"),el:e}),n||e===t.target?n=!0:i+=1}));for(var s=t.target.closest(".kg-card").nextElementSibling;s&&(s.classList.contains("kg-image-card")||s.classList.contains("kg-gallery-card"));)s.querySelectorAll("img").forEach(function(e){o.push({src:e.getAttribute("src"),msrc:e.getAttribute("src"),w:e.getAttribute("width"),h:e.getAttribute("height"),el:e})}),s=s.nextElementSibling;e=document.querySelectorAll(".pswp")[0];new PhotoSwipe(e,PhotoSwipeUI_Default,o,{bgOpacity:.9,closeOnScroll:!0,fullscreenEl:!1,history:!1,index:i,shareEl:!1,zoomEl:!1,getThumbBoundsFn:function(e){var e=o[e].el,t=window.pageYOffset||document.documentElement.scrollTop,e=e.getBoundingClientRect();return{x:e.left,y:e.top+t,w:e.width}}}).init()})})}function pagination(e=!0,s,l=!1){const c=document.querySelector(".gh-feed");if(!c)return;let t=!1;async function n(){var t=document.querySelector("link[rel=next]");if(t)try{var e=await(await fetch(t.href)).text(),n=(new DOMParser).parseFromString(e,"text/html"),o=n.querySelectorAll(".gh-feed:not(.gh-featured):not(.gh-related) > *");const r=document.createDocumentFragment(),a=[];o.forEach(function(e){e=document.importNode(e,!0);l&&(e.style.visibility="hidden"),r.appendChild(e),a.push(e)}),c.appendChild(r),s&&s(a,d);var i=n.querySelector("link[rel=next]");i&&i.href?t.href=i.href:(t.remove(),u&&u.remove())}catch(e){throw t.remove(),e}}const o=document.querySelector(".gh-footer"),u=document.querySelector(".gh-loadmore"),d=(!document.querySelector("link[rel=next]")&&u&&u.remove(),async function(){o.getBoundingClientRect().top<=window.innerHeight&&document.querySelector("link[rel=next]")&&await n()});const i=new IntersectionObserver(async function(e){if(!t){if(t=!0,e[0].isIntersecting)if(l)await n();else for(;o.getBoundingClientRect().top<=window.innerHeight&&document.querySelector("link[rel=next]");)await n();t=!1,document.querySelector("link[rel=next]")||i.disconnect()}});e?i.observe(o):u.addEventListener("click",n)}!function(e,t){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",t):"object"==typeof module&&module.exports?module.exports=t():e.EvEmitter=t()}("undefined"!=typeof window?window:this,function(){function e(){}var t=e.prototype;return t.on=function(e,t){var n;if(e&&t)return-1==(n=(n=this._events=this._events||{})[e]=n[e]||[]).indexOf(t)&&n.push(t),this},t.once=function(e,t){var n;if(e&&t)return this.on(e,t),((n=this._onceEvents=this._onceEvents||{})[e]=n[e]||{})[t]=!0,this},t.off=function(e,t){e=this._events&&this._events[e];if(e&&e.length)return-1!=(t=e.indexOf(t))&&e.splice(t,1),this},t.emitEvent=function(e,t){var n=this._events&&this._events[e];if(n&&n.length){n=n.slice(0),t=t||[];for(var o=this._onceEvents&&this._onceEvents[e],i=0;i<n.length;i++){var r=n[i];o&&o[r]&&(this.off(e,r),delete o[r]),r.apply(this,t)}return this}},t.allOff=function(){delete this._events,delete this._onceEvents},e}),function(t,n){"use strict";"function"==typeof define&&define.amd?define(["ev-emitter/ev-emitter"],function(e){return n(t,e)}):"object"==typeof module&&module.exports?module.exports=n(t,require("ev-emitter")):t.imagesLoaded=n(t,t.EvEmitter)}("undefined"!=typeof window?window:this,function(t,e){function r(e,t){for(var n in t)e[n]=t[n];return e}function a(e,t,n){var o,i;return this instanceof a?(o="string"==typeof(o=e)?document.querySelectorAll(e):o)?(this.elements=(i=o,Array.isArray(i)?i:"object"==typeof i&&"number"==typeof i.length?c.call(i):[i]),this.options=r({},this.options),"function"==typeof t?n=t:r(this.options,t),n&&this.on("always",n),this.getImages(),s&&(this.jqDeferred=new s.Deferred),void setTimeout(this.check.bind(this))):void l.error("Bad element for imagesLoaded "+(o||e)):new a(e,t,n)}function n(e){this.img=e}function o(e,t){this.url=e,this.element=t,this.img=new Image}var s=t.jQuery,l=t.console,c=Array.prototype.slice,u=((a.prototype=Object.create(e.prototype)).options={},a.prototype.getImages=function(){this.images=[],this.elements.forEach(this.addElementImages,this)},a.prototype.addElementImages=function(e){"IMG"==e.nodeName&&this.addImage(e),!0===this.options.background&&this.addElementBackgroundImages(e);var t=e.nodeType;if(t&&u[t]){for(var n=e.querySelectorAll("img"),o=0;o<n.length;o++){var i=n[o];this.addImage(i)}if("string"==typeof this.options.background)for(var r=e.querySelectorAll(this.options.background),o=0;o<r.length;o++){var a=r[o];this.addElementBackgroundImages(a)}}},{1:!0,9:!0,11:!0});return a.prototype.addElementBackgroundImages=function(e){var t=getComputedStyle(e);if(t)for(var n=/url\((['"])?(.*?)\1\)/gi,o=n.exec(t.backgroundImage);null!==o;){var i=o&&o[2];i&&this.addBackground(i,e),o=n.exec(t.backgroundImage)}},a.prototype.addImage=function(e){e=new n(e);this.images.push(e)},a.prototype.addBackground=function(e,t){e=new o(e,t);this.images.push(e)},a.prototype.check=function(){function t(e,t,n){setTimeout(function(){o.progress(e,t,n)})}var o=this;return this.progressedCount=0,this.hasAnyBroken=!1,this.images.length?void this.images.forEach(function(e){e.once("progress",t),e.check()}):void this.complete()},a.prototype.progress=function(e,t,n){this.progressedCount++,this.hasAnyBroken=this.hasAnyBroken||!e.isLoaded,this.emitEvent("progress",[this,e,t]),this.jqDeferred&&this.jqDeferred.notify&&this.jqDeferred.notify(this,e),this.progressedCount==this.images.length&&this.complete(),this.options.debug&&l&&l.log("progress: "+n,e,t)},a.prototype.complete=function(){var e=this.hasAnyBroken?"fail":"done";this.isComplete=!0,this.emitEvent(e,[this]),this.emitEvent("always",[this]),this.jqDeferred&&(e=this.hasAnyBroken?"reject":"resolve",this.jqDeferred[e](this))},(n.prototype=Object.create(e.prototype)).check=function(){return this.getIsImageComplete()?void this.confirm(0!==this.img.naturalWidth,"naturalWidth"):(this.proxyImage=new Image,this.proxyImage.addEventListener("load",this),this.proxyImage.addEventListener("error",this),this.img.addEventListener("load",this),this.img.addEventListener("error",this),void(this.proxyImage.src=this.img.src))},n.prototype.getIsImageComplete=function(){return this.img.complete&&this.img.naturalWidth},n.prototype.confirm=function(e,t){this.isLoaded=e,this.emitEvent("progress",[this,this.img,t])},n.prototype.handleEvent=function(e){var t="on"+e.type;this[t]&&this[t](e)},n.prototype.onload=function(){this.confirm(!0,"onload"),this.unbindEvents()},n.prototype.onerror=function(){this.confirm(!1,"onerror"),this.unbindEvents()},n.prototype.unbindEvents=function(){this.proxyImage.removeEventListener("load",this),this.proxyImage.removeEventListener("error",this),this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},(o.prototype=Object.create(n.prototype)).check=function(){this.img.addEventListener("load",this),this.img.addEventListener("error",this),this.img.src=this.url,this.getIsImageComplete()&&(this.confirm(0!==this.img.naturalWidth,"naturalWidth"),this.unbindEvents())},o.prototype.unbindEvents=function(){this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},o.prototype.confirm=function(e,t){this.isLoaded=e,this.emitEvent("progress",[this,this.element,t])},(a.makeJQueryPlugin=function(e){(e=e||t.jQuery)&&((s=e).fn.imagesLoaded=function(e,t){return new a(this,e,t).jqDeferred.promise(s(this))})})(),a}),function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipeUI_Default=t()}(this,function(){"use strict";return function(o,s){function e(e){if(S)return!0;e=e||window.event,T.timeToIdle&&T.mouseUsed&&!w&&l();for(var t,n,o=(e.target||e.srcElement).getAttribute("class")||"",i=0;i<R.length;i++)(t=R[i]).onTap&&-1<o.indexOf("pswp__"+t.name)&&(t.onTap(),n=!0);n&&(e.stopPropagation&&e.stopPropagation(),S=!0,e=s.features.isOldAndroid?600:30,setTimeout(function(){S=!1},e))}function n(){var e=1===T.getNumItemsFn();e!==C&&(O(p,"ui--one-slide",e),C=e)}function a(){O(v,"share-modal--hidden",M)}function i(){if((M=!M)?(s.removeClass(v,"pswp__share-modal--fade-in"),setTimeout(function(){M&&a()},300)):(a(),setTimeout(function(){M||s.addClass(v,"pswp__share-modal--fade-in")},30)),!M){for(var e,t,n,o,i="",r=0;r<T.shareButtons.length;r++)e=T.shareButtons[r],t=T.getImageURLForShare(e),n=T.getPageURLForShare(e),o=T.getTextForShare(e),i+='<a href="'+e.url.replace("{{url}}",encodeURIComponent(n)).replace("{{image_url}}",encodeURIComponent(t)).replace("{{raw_image_url}}",t).replace("{{text}}",encodeURIComponent(o))+'" target="_blank" class="pswp__share--'+e.id+'"'+(e.download?"download":"")+">"+e.label+"</a>",T.parseShareButtonOut&&(i=T.parseShareButtonOut(e,i));v.children[0].innerHTML=i,v.children[0].onclick=z}}function r(e){for(var t=0;t<T.closeElClasses.length;t++)if(s.hasClass(e,"pswp__"+T.closeElClasses[t]))return!0}function l(){clearTimeout(A),F=0,w&&D.setIdle(!1)}function c(e){(e=(e=e||window.event).relatedTarget||e.toElement)&&"HTML"!==e.nodeName||(clearTimeout(A),A=setTimeout(function(){D.setIdle(!0)},T.timeToIdleOutside))}function u(e){E!==e&&(O(b,"preloader--active",!e),E=e)}function d(e){var t,n=e.vGap;!o.likelyTouchDevice||T.mouseUsed||screen.width>T.fitControlsWidth?(t=T.barsSize,T.captionEl&&"auto"===t.bottom?(h||((h=s.createEl("pswp__caption pswp__caption--fake")).appendChild(s.createEl("pswp__caption__center")),p.insertBefore(h,f),s.addClass(p,"pswp__ui--fit")),T.addCaptionHTMLFn(e,h,!0)?(e=h.clientHeight,n.bottom=parseInt(e,10)||44):n.bottom=t.top):n.bottom="auto"===t.bottom?0:t.bottom,n.top=t.top):n.top=n.bottom=0}function P(){function e(e){if(e)for(var t=e.length,n=0;n<t;n++){i=e[n],r=i.className;for(var o=0;o<R.length;o++)a=R[o],-1<r.indexOf("pswp__"+a.name)&&(T[a.option]?(s.removeClass(i,"pswp__element--disabled"),a.onInit&&a.onInit(i)):s.addClass(i,"pswp__element--disabled"))}}e(p.children);var i,r,a,t=s.getChildByClass(p,"pswp__top-bar");t&&e(t.children)}var m,p,f,h,t,g,v,y,w,x,b,E,I,C,T,S,k,A,D=this,L=!1,_=!0,M=!0,Z={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(e,t){return e.title?(t.children[0].innerHTML=e.title,!0):(t.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"},{id:"download",label:"Download image",url:"{{raw_image_url}}",download:!0}],getImageURLForShare:function(){return o.currItem.src||""},getPageURLForShare:function(){return window.location.href},getTextForShare:function(){return o.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},O=function(e,t,n){s[(n?"add":"remove")+"Class"](e,"pswp__"+t)},z=function(e){var t=(e=e||window.event).target||e.srcElement;return o.shout("shareLinkClick",e,t),!(!t.href||!t.hasAttribute("download")&&(window.open(t.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),M||i(),1))},F=0,R=[{name:"caption",option:"captionEl",onInit:function(e){f=e}},{name:"share-modal",option:"shareEl",onInit:function(e){v=e},onTap:function(){i()}},{name:"button--share",option:"shareEl",onInit:function(e){g=e},onTap:function(){i()}},{name:"button--zoom",option:"zoomEl",onTap:o.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(e){t=e}},{name:"button--close",option:"closeEl",onTap:o.close},{name:"button--arrow--left",option:"arrowEl",onTap:o.prev},{name:"button--arrow--right",option:"arrowEl",onTap:o.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){m.isFullscreen()?m.exit():m.enter()}},{name:"preloader",option:"preloaderEl",onInit:function(e){b=e}}];D.init=function(){var t;s.extend(o.options,Z,!0),T=o.options,p=s.getChildByClass(o.scrollWrap,"pswp__ui"),(x=o.listen)("onVerticalDrag",function(e){_&&e<.95?D.hideControls():!_&&.95<=e&&D.showControls()}),x("onPinchClose",function(e){_&&e<.9?(D.hideControls(),t=!0):t&&!_&&.9<e&&D.showControls()}),x("zoomGestureEnded",function(){(t=!1)&&!_&&D.showControls()}),x("beforeChange",D.update),x("doubleTap",function(e){var t=o.currItem.initialZoomLevel;o.getZoomLevel()!==t?o.zoomTo(t,e,333):o.zoomTo(T.getDoubleTapZoom(!1,o.currItem),e,333)}),x("preventDragEvent",function(e,t,n){var o=e.target||e.srcElement;o&&o.getAttribute("class")&&-1<e.type.indexOf("mouse")&&(0<o.getAttribute("class").indexOf("__caption")||/(SMALL|STRONG|EM)/i.test(o.tagName))&&(n.prevent=!1)}),x("bindEvents",function(){s.bind(p,"pswpTap click",e),s.bind(o.scrollWrap,"pswpTap",D.onGlobalTap),o.likelyTouchDevice||s.bind(o.scrollWrap,"mouseover",D.onMouseOver)}),x("unbindEvents",function(){M||i(),k&&clearInterval(k),s.unbind(document,"mouseout",c),s.unbind(document,"mousemove",l),s.unbind(p,"pswpTap click",e),s.unbind(o.scrollWrap,"pswpTap",D.onGlobalTap),s.unbind(o.scrollWrap,"mouseover",D.onMouseOver),m&&(s.unbind(document,m.eventK,D.updateFullscreen),m.isFullscreen()&&(T.hideAnimationDuration=0,m.exit()),m=null)}),x("destroy",function(){T.captionEl&&(h&&p.removeChild(h),s.removeClass(f,"pswp__caption--empty")),v&&(v.children[0].onclick=null),s.removeClass(p,"pswp__ui--over-close"),s.addClass(p,"pswp__ui--hidden"),D.setIdle(!1)}),T.showAnimationDuration||s.removeClass(p,"pswp__ui--hidden"),x("initialZoomIn",function(){T.showAnimationDuration&&s.removeClass(p,"pswp__ui--hidden")}),x("initialZoomOut",function(){s.addClass(p,"pswp__ui--hidden")}),x("parseVerticalMargin",d),P(),T.shareEl&&g&&v&&(M=!0),n(),T.timeToIdle&&x("mouseUsed",function(){s.bind(document,"mousemove",l),s.bind(document,"mouseout",c),k=setInterval(function(){2===++F&&D.setIdle(!0)},T.timeToIdle/2)}),T.fullscreenEl&&!s.features.isOldAndroid&&((m=m||D.getFullscreenAPI())?(s.bind(document,m.eventK,D.updateFullscreen),D.updateFullscreen(),s.addClass(o.template,"pswp--supports-fs")):s.removeClass(o.template,"pswp--supports-fs")),T.preloaderEl&&(u(!0),x("beforeChange",function(){clearTimeout(I),I=setTimeout(function(){o.currItem&&o.currItem.loading?o.allowProgressiveImg()&&(!o.currItem.img||o.currItem.img.naturalWidth)||u(!1):u(!0)},T.loadingIndicatorDelay)}),x("imageLoadComplete",function(e,t){o.currItem===t&&u(!0)}))},D.setIdle=function(e){O(p,"ui--idle",w=e)},D.update=function(){L=!(!_||!o.currItem||(D.updateIndexIndicator(),T.captionEl&&(T.addCaptionHTMLFn(o.currItem,f),O(f,"caption--empty",!o.currItem.title)),0)),M||i(),n()},D.updateFullscreen=function(e){e&&setTimeout(function(){o.setScrollOffset(0,s.getScrollY())},50),s[(m.isFullscreen()?"add":"remove")+"Class"](o.template,"pswp--fs")},D.updateIndexIndicator=function(){T.counterEl&&(t.innerHTML=o.getCurrentIndex()+1+T.indexIndicatorSep+T.getNumItemsFn())},D.onGlobalTap=function(e){var t=(e=e||window.event).target||e.srcElement;if(!S)if(e.detail&&"mouse"===e.detail.pointerType)r(t)?o.close():s.hasClass(t,"pswp__img")&&(1===o.getZoomLevel()&&o.getZoomLevel()<=o.currItem.fitRatio?T.clickToCloseNonZoomable&&o.close():o.toggleDesktopZoom(e.detail.releasePoint));else if(T.tapToToggleControls&&(_?D.hideControls():D.showControls()),T.tapToClose&&(s.hasClass(t,"pswp__img")||r(t)))return void o.close()},D.onMouseOver=function(e){e=(e=e||window.event).target||e.srcElement;O(p,"ui--over-close",r(e))},D.hideControls=function(){s.addClass(p,"pswp__ui--hidden"),_=!1},D.showControls=function(){_=!0,L||D.update(),s.removeClass(p,"pswp__ui--hidden")},D.supportsFullscreen=function(){var e=document;return!!(e.exitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen||e.msExitFullscreen)},D.getFullscreenAPI=function(){var e,t=document.documentElement,n="fullscreenchange";return t.requestFullscreen?e={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:n}:t.mozRequestFullScreen?e={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+n}:t.webkitRequestFullscreen?e={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+n}:t.msRequestFullscreen&&(e={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),e&&(e.enter=function(){return y=T.closeOnScroll,T.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK?o.template[this.enterK]():void o.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},e.exit=function(){return T.closeOnScroll=y,document[this.exitK]()},e.isFullscreen=function(){return document[this.elementK]}),e}}}),function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipe=t()}(this,function(){"use strict";return function(m,Z,t,z){var p={features:null,bind:function(e,t,n,o){var i=(o?"remove":"add")+"EventListener";t=t.split(" ");for(var r=0;r<t.length;r++)t[r]&&e[i](t[r],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){t=document.createElement(t||"div");return e&&(t.className=e),t},getScrollY:function(){var e=window.pageYOffset;return void 0!==e?e:document.documentElement.scrollTop},unbind:function(e,t,n){p.bind(e,t,n,!0)},removeClass:function(e,t){t=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(t," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){p.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(p.hasClass(n,t))return n;n=n.nextSibling}},arraySearch:function(e,t,n){for(var o=e.length;o--;)if(e[o][n]===t)return o;return-1},extend:function(e,t,n){for(var o in t)if(t.hasOwnProperty(o)){if(n&&e.hasOwnProperty(o))continue;e[o]=t[o]}},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2}},cubic:{out:function(e){return--e*e*e+1}}},detectFeatures:function(){if(p.features)return p.features;var e,t,n=p.createEl().style,o="",i={};i.oldIE=document.all&&!document.addEventListener,i.touch="ontouchstart"in window,window.requestAnimationFrame&&(i.raf=window.requestAnimationFrame,i.caf=window.cancelAnimationFrame),i.pointerEvent=!!window.PointerEvent||navigator.msPointerEnabled,i.pointerEvent||(e=navigator.userAgent,/iP(hone|od)/.test(navigator.platform)&&(t=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/))&&0<t.length&&1<=(t=parseInt(t[1],10))&&t<8&&(i.isOldIOSPhone=!0),t=(t=e.match(/Android\s([0-9\.]*)/))?t[1]:0,1<=(t=parseFloat(t))&&(t<4.4&&(i.isOldAndroid=!0),i.androidVersion=t),i.isMobileOpera=/opera mini|opera mobi/i.test(e));for(var r,a,s,l=["transform","perspective","animationName"],c=["","webkit","Moz","ms","O"],u=0;u<4;u++){for(var o=c[u],d=0;d<3;d++)r=l[d],a=o+(o?r.charAt(0).toUpperCase()+r.slice(1):r),!i[r]&&a in n&&(i[r]=a);o&&!i.raf&&(o=o.toLowerCase(),i.raf=window[o+"RequestAnimationFrame"],i.raf)&&(i.caf=window[o+"CancelAnimationFrame"]||window[o+"CancelRequestAnimationFrame"])}return i.raf||(s=0,i.raf=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-s)),o=window.setTimeout(function(){e(t+n)},n);return s=t+n,o},i.caf=function(e){clearTimeout(e)}),i.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,p.features=i}},f=(p.detectFeatures(),p.features.oldIE&&(p.bind=function(e,t,n,o){t=t.split(" ");for(var i,r=(o?"detach":"attach")+"Event",a=function(){n.handleEvent.call(n)},s=0;s<t.length;s++)if(i=t[s])if("object"==typeof n&&n.handleEvent){if(o){if(!n["oldIE"+i])return!1}else n["oldIE"+i]=a;e[r]("on"+i,n["oldIE"+i])}else e[r]("on"+i,n)}),this),q=25,h={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return"A"===e.tagName},getDoubleTapZoom:function(e,t){return e||t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};p.extend(h,z);function e(){return{x:0,y:0}}function N(e,t){p.extend(f,t.publicMethods),Ke.push(e)}function B(e){var t=O();return t-1<e?e-t:e<0?t+e:e}function r(e,t){return Ge[e]||(Ge[e]=[]),Ge[e].push(t)}function U(e,t,n,o){o===f.currItem.initialZoomLevel?n[e]=f.currItem.initialPosition[e]:(n[e]=Je(e,o),n[e]>t.min[e]?n[e]=t.min[e]:n[e]<t.max[e]&&(n[e]=t.max[e]))}function W(e){var t="";h.escKey&&27===e.keyCode?t="close":h.arrowKeys&&(37===e.keyCode?t="prev":39===e.keyCode&&(t="next")),!t||e.ctrlKey||e.altKey||e.shiftKey||e.metaKey||(e.preventDefault?e.preventDefault():e.returnValue=!1,f[t]())}function H(e){e&&(De||Ae||y||Te)&&(e.preventDefault(),e.stopPropagation())}function K(){f.setScrollOffset(0,p.getScrollY())}function j(e){var t;"mousedown"===e.type&&0<e.button||(Jt?e.preventDefault():Se&&"mousedown"===e.type||(_t(e,!0)&&e.preventDefault(),I("pointerDown"),pe&&((t=p.arraySearch(ht,e.pointerId,"id"))<0&&(t=ht.length),ht[t]={x:e.pageX,y:e.pageY,id:e.pointerId}),e=(t=qt(e)).length,c=null,ct(),l&&1!==e||(l=Re=!0,p.bind(window,ee,f),Ce=ze=Pe=Te=_e=De=ke=Ae=!1,Fe=null,I("firstTouchStart",t),k(Be,w),Ne.x=Ne.y=0,k(_,t[0]),k(ft,_),gt.x=b.x*Ue,vt=[{x:_.x,y:_.y}],Ee=be=C(),ot(v,!0),St(),kt()),!u&&1<e&&!y&&!_e&&(ne=v,u=ke=!(Ae=!1),Ne.y=Ne.x=0,k(Be,w),k(D,t[0]),k(pt,t[1]),Ot(D,pt,It),Et.x=Math.abs(It.x)-w.x,Et.y=Math.abs(It.y)-w.y,Me=Tt(D,pt))))}function Y(e){var t;e.preventDefault(),pe&&-1<(t=p.arraySearch(ht,e.pointerId,"id"))&&((t=ht[t]).x=e.pageX,t.y=e.pageY),l&&(t=qt(e),Fe||De||u?c=t:M.x!==b.x*Ue?Fe="h":(e=Math.abs(t[0].x-_.x)-Math.abs(t[0].y-_.y),Math.abs(e)>=mt&&(Fe=0<e?"h":"v",c=t)))}function G(e){if(s.isOldAndroid){if(Se&&"mouseup"===e.type)return;-1<e.type.indexOf("touch")&&(clearTimeout(Se),Se=setTimeout(function(){Se=0},600))}var t;I("pointerUp"),_t(e,!1)&&e.preventDefault(),pe&&-1<(r=p.arraySearch(ht,e.pointerId,"id"))&&(t=ht.splice(r,1)[0],navigator.msPointerEnabled&&(t.type={4:"mouse",2:"touch",3:"pen"}[e.pointerType],t.type)||(t.type=e.pointerType||"mouse"));var n=(r=qt(e)).length;if(2===(n="mouseup"===e.type?0:n))return!(c=null);1===n&&k(ft,r[0]),0!==n||Fe||y||(t||("mouseup"===e.type?t={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(t={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),I("touchRelease",e,t));var o,i,r=-1;if(0===n&&(l=!1,p.unbind(window,ee,f),St(),u?r=0:-1!==bt&&(r=C()-bt)),bt=1===n?C():-1,e=-1!==r&&r<150?"zoom":"swipe",u&&n<2&&(u=!1,1===n&&(e="zoomPointerUp"),I("zoomGestureEnded")),c=null,De||Ae||y||Te)if(ct(),(Ie=Ie||Ut()).calculateSwipeSpeed("x"),Te)Rt()<h.verticalDragRange?f.close():(o=w.y,i=Ze,ut("verticalDrag",0,1,300,p.easing.cubic.out,function(e){w.y=(f.currItem.initialPosition.y-o)*e+o,T((1-i)*e+i),S()}),I("onVerticalDrag",1));else{if((_e||y)&&0===n){if(Ht(e,Ie))return;e="zoomPointerUp"}if(!y)return"swipe"!==e?void jt():void(!_e&&v>f.currItem.fitRatio&&Wt(Ie))}}var V,X,$,g,Q,J,ee,te,o,v,ne,oe,ie,re,ae,a,se,le,ce,ue,de,me,pe,i,fe,he,ge,ve,ye,we,s,xe,be,Ee,Ie,Ce,Te,Se,l,ke,Ae,De,Le,_e,c,u,Me,d,Oe,y,Fe,Re,Pe,Ze,ze,qe,Ne=e(),Be=e(),w=e(),x={},Ue=0,We={},b=e(),E=0,He=!0,Ke=[],je={},Ye=!1,Ge={},I=function(e){var t=Ge[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var o=0;o<t.length;o++)t[o].apply(f,n)}},C=function(){return(new Date).getTime()},T=function(e){Ze=e,f.bg.style.opacity=e*h.bgOpacity},Ve=function(e,t,n,o,i){(!Ye||i&&i!==f.currItem)&&(o/=(i||f.currItem).fitRatio),e[me]=oe+t+"px, "+n+"px"+ie+" scale("+o+")"},S=function(e){Oe&&(e&&(v>f.currItem.fitRatio?Ye||(cn(f.currItem,!1,!0),Ye=!0):Ye&&(cn(f.currItem),Ye=!1)),Ve(Oe,w.x,w.y,v))},Xe=function(e){e.container&&Ve(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},$e=function(e,t){t[me]=oe+e+"px, 0px"+ie},Qe=function(e,t){var n;!h.loop&&t&&(t=g+(b.x*Ue-e)/b.x,n=Math.round(e-M.x),t<0&&0<n||t>=O()-1&&n<0)&&(e=M.x+n*h.mainScrollEndFriction),M.x=e,$e(e,Q)},Je=function(e,t){var n=Et[e]-We[e];return Be[e]+Ne[e]+n-t/ne*n},k=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},et=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},tt=null,nt=function(){tt&&(p.unbind(document,"mousemove",nt),p.addClass(m,"pswp--has_mouse"),h.mouseUsed=!0,I("mouseUsed")),tt=setTimeout(function(){tt=null},100)},ot=function(e,t){e=sn(f.currItem,x,e);return t&&(d=e),e},it=function(e){return(e=e||f.currItem).initialZoomLevel},rt=function(e){return 0<(e=e||f.currItem).w?h.maxSpreadZoom:1},A={},at=0,st=function(e){A[e]&&(A[e].raf&&he(A[e].raf),at--,delete A[e])},lt=function(e){A[e]&&st(e),A[e]||(at++,A[e]={})},ct=function(){for(var e in A)A.hasOwnProperty(e)&&st(e)},ut=function(e,t,n,o,i,r,a){function s(){A[e]&&(l=C()-c,o<=l?(st(e),r(n),a&&a()):(r((n-t)*i(l/o)+t),A[e].raf=fe(s)))}var l,c=C();lt(e);s()},z={shout:I,listen:r,viewportSize:x,options:h,isMainScrollAnimating:function(){return y},getZoomLevel:function(){return v},getCurrentIndex:function(){return g},isDragging:function(){return l},isZooming:function(){return u},setScrollOffset:function(e,t){We.x=e,we=We.y=t,I("updateScrollOffset",We)},applyZoomPan:function(e,t,n,o){w.x=t,w.y=n,v=e,S(o)},init:function(){if(!V&&!X){f.framework=p,f.template=m,f.bg=p.getChildByClass(m,"pswp__bg"),ge=m.className,V=!0,s=p.detectFeatures(),fe=s.raf,he=s.caf,me=s.transform,ye=s.oldIE,f.scrollWrap=p.getChildByClass(m,"pswp__scroll-wrap"),f.container=p.getChildByClass(f.scrollWrap,"pswp__container"),Q=f.container.style,f.itemHolders=a=[{el:f.container.children[0],wrap:0,index:-1},{el:f.container.children[1],wrap:0,index:-1},{el:f.container.children[2],wrap:0,index:-1}],a[0].el.style.display=a[2].el.style.display="none",me?(t=s.perspective&&!i,oe="translate"+(t?"3d(":"("),ie=s.perspective?", 0px)":")"):(me="left",p.addClass(m,"pswp--ie"),$e=function(e,t){t.left=e+"px"},Xe=function(e){var t=1<e.fitRatio?1:e.fitRatio,n=e.container.style,o=t*e.w,t=t*e.h;n.width=o+"px",n.height=t+"px",n.left=e.initialPosition.x+"px",n.top=e.initialPosition.y+"px"},S=function(){var e,t,n,o;Oe&&(e=Oe,n=(o=1<(t=f.currItem).fitRatio?1:t.fitRatio)*t.w,o=o*t.h,e.width=n+"px",e.height=o+"px",e.left=w.x+"px",e.top=w.y+"px")}),o={resize:f.updateSize,orientationchange:function(){clearTimeout(xe),xe=setTimeout(function(){x.x!==f.scrollWrap.clientWidth&&f.updateSize()},500)},scroll:K,keydown:W,click:H};var e,t=s.isOldIOSPhone||s.isOldAndroid||s.isMobileOpera;for(s.animationName&&s.transform&&!t||(h.showAnimationDuration=h.hideAnimationDuration=0),e=0;e<Ke.length;e++)f["init"+Ke[e]]();Z&&(f.ui=new Z(f,p)).init(),I("firstUpdate"),g=g||h.index||0,(isNaN(g)||g<0||g>=O())&&(g=0),f.currItem=en(g),(s.isOldIOSPhone||s.isOldAndroid)&&(He=!1),m.setAttribute("aria-hidden","false"),h.modal&&(He?m.style.position="fixed":(m.style.position="absolute",m.style.top=p.getScrollY()+"px")),void 0===we&&(I("initialLayout"),we=ve=p.getScrollY());var n="pswp--open ";for(h.mainClass&&(n+=h.mainClass+" "),h.showHideOpacity&&(n+="pswp--animate_opacity "),n=(n=(n+=i?"pswp--touch":"pswp--notouch")+(s.animationName?" pswp--css_animation":""))+(s.svg?" pswp--svg":""),p.addClass(m,n),f.updateSize(),J=-1,E=null,e=0;e<3;e++)$e((e+J)*b.x,a[e].el.style);ye||p.bind(f.scrollWrap,te,f),r("initialZoomInEnd",function(){f.setContent(a[0],g-1),f.setContent(a[2],g+1),a[0].el.style.display=a[2].el.style.display="block",h.focus&&m.focus(),p.bind(document,"keydown",f),s.transform&&p.bind(f.scrollWrap,"click",f),h.mouseUsed||p.bind(document,"mousemove",nt),p.bind(window,"resize scroll orientationchange",f),I("bindEvents")}),f.setContent(a[1],g),f.updateCurrItem(),I("afterInit"),He||(re=setInterval(function(){at||l||u||v!==f.currItem.initialZoomLevel||f.updateSize()},1e3)),p.addClass(m,"pswp--visible")}var t},close:function(){V&&(X=!(V=!1),I("close"),p.unbind(window,"resize scroll orientationchange",f),p.unbind(window,"scroll",o.scroll),p.unbind(document,"keydown",f),p.unbind(document,"mousemove",nt),s.transform&&p.unbind(f.scrollWrap,"click",f),l&&p.unbind(window,ee,f),clearTimeout(xe),I("unbindEvents"),tn(f.currItem,null,!0,f.destroy))},destroy:function(){I("destroy"),Xt&&clearTimeout(Xt),m.setAttribute("aria-hidden","true"),m.className=ge,re&&clearInterval(re),p.unbind(f.scrollWrap,te,f),p.unbind(window,"scroll",f),St(),ct(),Ge=null},panTo:function(e,t,n){n||(e>d.min.x?e=d.min.x:e<d.max.x&&(e=d.max.x),t>d.min.y?t=d.min.y:t<d.max.y&&(t=d.max.y)),w.x=e,w.y=t,S()},handleEvent:function(e){e=e||window.event,o[e.type]&&o[e.type](e)},goTo:function(e){var t=(e=B(e))-g;E=t,g=e,f.currItem=en(g),Ue-=t,Qe(b.x*Ue),ct(),y=!1,f.updateCurrItem()},next:function(){f.goTo(g+1)},prev:function(){f.goTo(g-1)},updateCurrZoomItem:function(e){var t;e&&I("beforeChange",0),Oe=a[1].el.children.length&&(t=a[1].el.children[0],p.hasClass(t,"pswp__zoom-wrap"))?t.style:null,d=f.currItem.bounds,ne=v=f.currItem.initialZoomLevel,w.x=d.center.x,w.y=d.center.y,e&&I("afterChange")},invalidateCurrItems:function(){ae=!0;for(var e=0;e<3;e++)a[e].item&&(a[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(0!==E){var t,n=Math.abs(E);if(!(e&&n<2)){f.currItem=en(g),Ye=!1,I("beforeChange",E),3<=n&&(J+=E+(0<E?-3:3),n=3);for(var o=0;o<n;o++)0<E?(t=a.shift(),a[2]=t,$e((++J+2)*b.x,t.el.style),f.setContent(t,g-n+o+1+1)):(t=a.pop(),a.unshift(t),$e(--J*b.x,t.el.style),f.setContent(t,g+n-o-1-1));Oe&&1===Math.abs(E)&&(e=en(se)).initialZoomLevel!==v&&(sn(e,x),cn(e),Xe(e)),E=0,f.updateCurrZoomItem(),se=g,I("afterChange")}}},updateSize:function(e){if(!He&&h.modal){var t=p.getScrollY();if(we!==t&&(m.style.top=t+"px",we=t),!e&&je.x===window.innerWidth&&je.y===window.innerHeight)return;je.x=window.innerWidth,je.y=window.innerHeight,m.style.height=je.y+"px"}if(x.x=f.scrollWrap.clientWidth,x.y=f.scrollWrap.clientHeight,K(),b.x=x.x+Math.round(x.x*h.spacing),b.y=x.y,Qe(b.x*Ue),I("beforeResize"),void 0!==J){for(var n,o,i,r=0;r<3;r++)n=a[r],$e((r+J)*b.x,n.el.style),i=g+r-1,h.loop&&2<O()&&(i=B(i)),(o=en(i))&&(ae||o.needsUpdate||!o.bounds)?(f.cleanSlide(o),f.setContent(n,i),1===r&&(f.currItem=o,f.updateCurrZoomItem(!0)),o.needsUpdate=!1):-1===n.index&&0<=i&&f.setContent(n,i),o&&o.container&&(sn(o,x),cn(o),Xe(o));ae=!1}ne=v=f.currItem.initialZoomLevel,(d=f.currItem.bounds)&&(w.x=d.center.x,w.y=d.center.y,S(!0)),I("resize")},zoomTo:function(t,e,n,o,i){e&&(ne=v,Et.x=Math.abs(e.x)-w.x,Et.y=Math.abs(e.y)-w.y,k(Be,w));function r(e){1===e?(v=t,w.x=a.x,w.y=a.y):(v=(t-s)*e+s,w.x=(a.x-l.x)*e+l.x,w.y=(a.y-l.y)*e+l.y),i&&i(e),S(1===e)}var e=ot(t,!1),a={},s=(U("x",e,a,t),U("y",e,a,t),v),l={x:w.x,y:w.y};et(a);n?ut("customZoomTo",0,1,n,o||p.easing.sine.inOut,r):r(1)}},dt=30,mt=10,D={},pt={},L={},_={},ft={},ht=[],gt={},vt=[],yt={},wt=0,xt=e(),bt=0,M=e(),Et=e(),It=e(),Ct=function(e,t){return e.x===t.x&&e.y===t.y},Tt=function(e,t){return yt.x=Math.abs(e.x-t.x),yt.y=Math.abs(e.y-t.y),Math.sqrt(yt.x*yt.x+yt.y*yt.y)},St=function(){Le&&(he(Le),Le=null)},kt=function(){l&&(Le=fe(kt),Bt())},At=function(){return!("fit"===h.scaleMode&&v===f.currItem.initialZoomLevel)},Dt=function(e,t){return!(!e||e===document)&&!(e.getAttribute("class")&&-1<e.getAttribute("class").indexOf("pswp__scroll-wrap"))&&(t(e)?e:Dt(e.parentNode,t))},Lt={},_t=function(e,t){return Lt.prevent=!Dt(e.target,h.isClickableElement),I("preventDragEvent",e,t,Lt),Lt.prevent},Mt=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},Ot=function(e,t,n){n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y)},Ft=function(e,t,n){var o;50<e-Ee&&((o=2<vt.length?vt.shift():{}).x=t,o.y=n,vt.push(o),Ee=e)},Rt=function(){var e=w.y-f.currItem.initialPosition.y;return 1-Math.abs(e/(x.y/2))},Pt={},Zt={},zt=[],qt=function(e){for(;0<zt.length;)zt.pop();return pe?(qe=0,ht.forEach(function(e){0===qe?zt[0]=e:1===qe&&(zt[1]=e),qe++})):-1<e.type.indexOf("touch")?e.touches&&0<e.touches.length&&(zt[0]=Mt(e.touches[0],Pt),1<e.touches.length)&&(zt[1]=Mt(e.touches[1],Zt)):(Pt.x=e.pageX,Pt.y=e.pageY,Pt.id="",zt[0]=Pt),zt},Nt=function(e,t){var n,o,i,r=w[e]+t[e],a=0<t[e],s=M.x+t.x,l=M.x-gt.x,c=r>d.min[e]||r<d.max[e]?h.panEndFriction:1,r=w[e]+t[e]*c;return!h.allowPanToNext&&v!==f.currItem.initialZoomLevel||(Oe?"h"!==Fe||"x"!==e||Ae||(a?(r>d.min[e]&&(c=h.panEndFriction,d.min[e],n=d.min[e]-Be[e]),(n<=0||l<0)&&1<O()?(i=s,l<0&&s>gt.x&&(i=gt.x)):d.min.x!==d.max.x&&(o=r)):(r<d.max[e]&&(c=h.panEndFriction,d.max[e],n=Be[e]-d.max[e]),(n<=0||0<l)&&1<O()?(i=s,0<l&&s<gt.x&&(i=gt.x)):d.min.x!==d.max.x&&(o=r))):i=s,"x"!==e)?void(y||_e||v>f.currItem.fitRatio&&(w[e]+=t[e]*c)):(void 0!==i&&(Qe(i,!0),_e=i!==gt.x),d.min.x!==d.max.x&&(void 0!==o?w.x=o:_e||(w.x+=t.x*c)),void 0!==i)},Bt=function(){var e,t,n,o,i,r;c&&0!==(e=c.length)&&(k(D,c[0]),L.x=D.x-_.x,L.y=D.y-_.y,u&&1<e?(_.x=D.x,_.y=D.y,!L.x&&!L.y&&Ct(c[1],pt)||(k(pt,c[1]),Ae||(Ae=!0,I("zoomGestureStarted")),e=Tt(D,pt),(t=Kt(e))>f.currItem.initialZoomLevel+f.currItem.initialZoomLevel/15&&(ze=!0),n=1,o=it(),i=rt(),t<o?h.pinchToClose&&!ze&&ne<=f.currItem.initialZoomLevel?(T(r=1-(o-t)/(o/1.2)),I("onPinchClose",r),Pe=!0):t=o-(n=1<(n=(o-t)/o)?1:n)*(o/3):i<t&&(t=i+(n=1<(n=(t-i)/(6*o))?1:n)*o),n<0&&(n=0),Ot(D,pt,xt),Ne.x+=xt.x-It.x,Ne.y+=xt.y-It.y,k(It,xt),w.x=Je("x",t),w.y=Je("y",t),Ce=v<t,v=t,S())):Fe&&(Re&&(Re=!1,Math.abs(L.x)>=mt&&(L.x-=c[0].x-ft.x),Math.abs(L.y)>=mt)&&(L.y-=c[0].y-ft.y),_.x=D.x,_.y=D.y,0===L.x&&0===L.y||("v"===Fe&&h.closeOnVerticalDrag&&!At()?(Ne.y+=L.y,w.y+=L.y,r=Rt(),Te=!0,I("onVerticalDrag",r),T(r),S()):(Ft(C(),D.x,D.y),De=!0,d=f.currItem.bounds,Nt("x",L)||(Nt("y",L),et(w),S())))))},Ut=function(){var t,n,o={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(e){n=(1<vt.length?(t=C()-Ee+50,vt[vt.length-2]):(t=C()-be,ft))[e],o.lastFlickOffset[e]=_[e]-n,o.lastFlickDist[e]=Math.abs(o.lastFlickOffset[e]),20<o.lastFlickDist[e]?o.lastFlickSpeed[e]=o.lastFlickOffset[e]/t:o.lastFlickSpeed[e]=0,Math.abs(o.lastFlickSpeed[e])<.1&&(o.lastFlickSpeed[e]=0),o.slowDownRatio[e]=.95,o.slowDownRatioReverse[e]=1-o.slowDownRatio[e],o.speedDecelerationRatio[e]=1},calculateOverBoundsAnimOffset:function(t,e){o.backAnimStarted[t]||(w[t]>d.min[t]?o.backAnimDestination[t]=d.min[t]:w[t]<d.max[t]&&(o.backAnimDestination[t]=d.max[t]),void 0!==o.backAnimDestination[t]&&(o.slowDownRatio[t]=.7,o.slowDownRatioReverse[t]=1-o.slowDownRatio[t],o.speedDecelerationRatioAbs[t]<.05)&&(o.lastFlickSpeed[t]=0,o.backAnimStarted[t]=!0,ut("bounceZoomPan"+t,w[t],o.backAnimDestination[t],e||300,p.easing.sine.out,function(e){w[t]=e,S()})))},calculateAnimOffset:function(e){o.backAnimStarted[e]||(o.speedDecelerationRatio[e]=o.speedDecelerationRatio[e]*(o.slowDownRatio[e]+o.slowDownRatioReverse[e]-o.slowDownRatioReverse[e]*o.timeDiff/10),o.speedDecelerationRatioAbs[e]=Math.abs(o.lastFlickSpeed[e]*o.speedDecelerationRatio[e]),o.distanceOffset[e]=o.lastFlickSpeed[e]*o.speedDecelerationRatio[e]*o.timeDiff,w[e]+=o.distanceOffset[e])},panAnimLoop:function(){A.zoomPan&&(A.zoomPan.raf=fe(o.panAnimLoop),o.now=C(),o.timeDiff=o.now-o.lastNow,o.lastNow=o.now,o.calculateAnimOffset("x"),o.calculateAnimOffset("y"),S(),o.calculateOverBoundsAnimOffset("x"),o.calculateOverBoundsAnimOffset("y"),o.speedDecelerationRatioAbs.x<.05)&&o.speedDecelerationRatioAbs.y<.05&&(w.x=Math.round(w.x),w.y=Math.round(w.y),S(),st("zoomPan"))}};return o},Wt=function(e){return e.calculateSwipeSpeed("y"),d=f.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05?(e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0):(lt("zoomPan"),e.lastNow=C(),void e.panAnimLoop())},Ht=function(e,t){var n,o,i;y||(wt=g),"swipe"===e&&(e=_.x-ft.x,r=t.lastFlickDist.x<10,dt<e&&(r||20<t.lastFlickOffset.x)?o=-1:e<-dt&&(r||t.lastFlickOffset.x<-20)&&(o=1)),o&&((g+=o)<0?(g=h.loop?O()-1:0,i=!0):g>=O()&&(g=h.loop?0:O()-1,i=!0),i&&!h.loop||(E+=o,Ue-=o,n=!0));var e=b.x*Ue,r=Math.abs(e-M.x),a=n||e>M.x==0<t.lastFlickSpeed.x?(a=0<Math.abs(t.lastFlickSpeed.x)?r/Math.abs(t.lastFlickSpeed.x):333,a=Math.min(a,400),Math.max(a,250)):333;return wt===g&&(n=!1),y=!0,I("mainScrollAnimStart"),ut("mainScroll",M.x,e,a,p.easing.cubic.out,Qe,function(){ct(),y=!1,wt=-1,!n&&wt===g||f.updateCurrItem(),I("mainScrollAnimComplete")}),n&&f.updateCurrItem(!0),n},Kt=function(e){return 1/Me*e*ne},jt=function(){var e=v,t=it(),n=rt();v<t?e=t:n<v&&(e=n);var o,i=Ze;return Pe&&!Ce&&!ze&&v<t?f.close():(Pe&&(o=function(e){T((1-i)*e+i)}),f.zoomTo(e,0,200,p.easing.cubic.out,o)),!0};N("Gestures",{publicMethods:{initGestures:function(){function e(e,t,n,o,i){le=e+t,ce=e+n,ue=e+o,de=i?e+i:""}(pe=s.pointerEvent)&&s.touch&&(s.touch=!1),pe?navigator.msPointerEnabled?e("MSPointer","Down","Move","Up","Cancel"):e("pointer","down","move","up","cancel"):s.touch?(e("touch","start","move","end","cancel"),i=!0):e("mouse","down","move","up"),ee=ce+" "+ue+" "+de,te=le,pe&&!i&&(i=1<navigator.maxTouchPoints||1<navigator.msMaxTouchPoints),f.likelyTouchDevice=i,o[le]=j,o[ce]=Y,o[ue]=G,de&&(o[de]=o[ue]),s.touch&&(te+=" mousedown",ee+=" mousemove mouseup",o.mousedown=o[le],o.mousemove=o[ce],o.mouseup=o[ue]),i||(h.allowPanToNext=!1)}}});function Yt(e){function t(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,n.onload=n.onerror=null,n=null}e.loading=!0,e.loaded=!1;var n=e.img=p.createEl("pswp__img","img");n.onload=t,n.onerror=function(){e.loadError=!0,t()},n.src=e.src}function Gt(e,t){return e.src&&e.loadError&&e.container&&(t&&(e.container.innerHTML=""),e.container.innerHTML=h.errorMsg.replace("%url%",e.src),1)}function Vt(){if(nn.length){for(var e,t=0;t<nn.length;t++)(e=nn[t]).holder.index===e.index&&ln(e.index,e.item,e.baseDiv,e.img,!1,e.clearPlaceholder);nn=[]}}var Xt,$t,Qt,Jt,en,O,tn=function(r,e,a,t){function s(){st("initialZoom"),a?(f.template.removeAttribute("style"),f.bg.removeAttribute("style")):(T(1),e&&(e.style.display="block"),p.addClass(m,"pswp--animated-in"),I("initialZoom"+(a?"OutEnd":"InEnd"))),t&&t(),Jt=!1}Xt&&clearTimeout(Xt),Qt=Jt=!0,r.initialLayout?(l=r.initialLayout,r.initialLayout=null):l=h.getThumbBoundsFn&&h.getThumbBoundsFn(g);var l,c,u,d=a?h.hideAnimationDuration:h.showAnimationDuration;d&&l&&void 0!==l.x?(c=$,u=!f.currItem.src||f.currItem.loadError||h.showHideOpacity,r.miniImg&&(r.miniImg.style.webkitBackfaceVisibility="hidden"),a||(v=l.w/r.w,w.x=l.x,w.y=l.y-ve,f[u?"template":"bg"].style.opacity=.001,S()),lt("initialZoom"),a&&!c&&p.removeClass(m,"pswp--animated-in"),u&&(a?p[(c?"remove":"add")+"Class"](m,"pswp--animate_opacity"):setTimeout(function(){p.addClass(m,"pswp--animate_opacity")},30)),Xt=setTimeout(function(){var t,n,o,i,e;I("initialZoom"+(a?"Out":"In")),a?(t=l.w/r.w,n={x:w.x,y:w.y},o=v,i=Ze,e=function(e){1===e?(v=t,w.x=l.x,w.y=l.y-we):(v=(t-o)*e+o,w.x=(l.x-n.x)*e+n.x,w.y=(l.y-we-n.y)*e+n.y),S(),u?m.style.opacity=1-e:T(i-e*i)},c?ut("initialZoom",0,1,d,p.easing.cubic.out,e,s):(e(1),Xt=setTimeout(s,d+20))):(v=r.initialZoomLevel,k(w,r.initialPosition),S(),T(1),u?m.style.opacity=1:T(1),Xt=setTimeout(s,d+20))},a?25:90)):(I("initialZoom"+(a?"Out":"In")),v=r.initialZoomLevel,k(w,r.initialPosition),S(),m.style.opacity=a?0:1,T(1),d?setTimeout(function(){s()},d):s())},F={},nn=[],on={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return $t.length}},rn=function(){return{center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}}},an=function(e,t,n){var o=e.bounds;o.center.x=Math.round((F.x-t)/2),o.center.y=Math.round((F.y-n)/2)+e.vGap.top,o.max.x=t>F.x?Math.round(F.x-t):o.center.x,o.max.y=n>F.y?Math.round(F.y-n)+e.vGap.top:o.center.y,o.min.x=t>F.x?0:o.center.x,o.min.y=n>F.y?e.vGap.top:o.center.y},sn=function(e,t,n){var o,i;return e.src&&!e.loadError?((o=!n)&&(e.vGap||(e.vGap={top:0,bottom:0}),I("parseVerticalMargin",e)),F.x=t.x,F.y=t.y-e.vGap.top-e.vGap.bottom,o&&(t=F.x/e.w,i=F.y/e.h,e.fitRatio=t<i?t:i,"orig"===(t=h.scaleMode)?n=1:"fit"===t&&(n=e.fitRatio),e.initialZoomLevel=n=1<n?1:n,e.bounds||(e.bounds=rn())),n?(an(e,e.w*n,e.h*n),o&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds):void 0):(e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds=rn(),e.initialPosition=e.bounds.center,e.bounds)},ln=function(e,t,n,o,i,r){t.loadError||o&&(t.imageAppended=!0,cn(t,o,t===f.currItem&&Ye),n.appendChild(o),r)&&setTimeout(function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)},500)},cn=function(e,t,n){var o;e.src&&(t=t||e.container.lastChild,o=n?e.w:Math.round(e.w*e.fitRatio),n=n?e.h:Math.round(e.h*e.fitRatio),e.placeholder&&!e.loaded&&(e.placeholder.style.width=o+"px",e.placeholder.style.height=n+"px"),t.style.width=o+"px",t.style.height=n+"px")};N("Controller",{publicMethods:{lazyLoadItem:function(e){e=B(e);var t=en(e);t&&(!t.loaded&&!t.loading||ae)&&(I("gettingData",e,t),t.src)&&Yt(t)},initController:function(){p.extend(h,on,!0),f.items=$t=t,en=f.getItemAt,O=h.getNumItemsFn,h.loop,O()<3&&(h.loop=!1),r("beforeChange",function(e){for(var t=h.preload,n=null===e||0<=e,o=Math.min(t[0],O()),i=Math.min(t[1],O()),r=1;r<=(n?i:o);r++)f.lazyLoadItem(g+r);for(r=1;r<=(n?o:i);r++)f.lazyLoadItem(g-r)}),r("initialLayout",function(){f.currItem.initialLayout=h.getThumbBoundsFn&&h.getThumbBoundsFn(g)}),r("mainScrollAnimComplete",Vt),r("initialZoomInEnd",Vt),r("destroy",function(){for(var e,t=0;t<$t.length;t++)(e=$t[t]).container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);nn=null})},getItemAt:function(e){return 0<=e&&void 0!==$t[e]&&$t[e]},allowProgressiveImg:function(){return h.forceProgressiveLoading||!i||h.mouseUsed||1200<screen.width},setContent:function(t,n){h.loop&&(n=B(n));var e=f.getItemAt(t.index);e&&(e.container=null);var o,i,r,e=f.getItemAt(n);e?(I("gettingData",n,e),t.index=n,i=(t.item=e).container=p.createEl("pswp__zoom-wrap"),!e.src&&e.html&&(e.html.tagName?i.appendChild(e.html):i.innerHTML=e.html),Gt(e),sn(e,x),!e.src||e.loadError||e.loaded?e.src&&!e.loadError&&((o=p.createEl("pswp__img","img")).style.opacity=1,o.src=e.src,cn(e,o),ln(n,e,i,o,!0)):(e.loadComplete=function(e){if(V){if(t&&t.index===n){if(Gt(e,!0))return e.loadComplete=e.img=null,sn(e,x),Xe(e),void(t.index===g&&f.updateCurrZoomItem());e.imageAppended?!Jt&&e.placeholder&&(e.placeholder.style.display="none",e.placeholder=null):s.transform&&(y||Jt)?nn.push({item:e,baseDiv:i,img:e.img,index:n,holder:t,clearPlaceholder:!0}):ln(n,e,i,e.img,y||Jt,!0)}e.loadComplete=null,e.img=null,I("imageLoadComplete",n,e)}},p.features.transform&&(r="pswp__img pswp__img--placeholder",r+=e.msrc?"":" pswp__img--placeholder--blank",r=p.createEl(r,e.msrc?"img":""),e.msrc&&(r.src=e.msrc),cn(e,r),i.appendChild(r),e.placeholder=r),e.loading||Yt(e),f.allowProgressiveImg()&&(!Qt&&s.transform?nn.push({item:e,baseDiv:i,img:e.img,index:n,holder:t}):ln(n,e,i,e.img,!0,!0))),Qt||n!==g?Xe(e):(Oe=i.style,tn(e,o||e.img)),t.el.innerHTML="",t.el.appendChild(i)):t.el.innerHTML=""},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1}}});function un(e,t,n){var o=document.createEvent("CustomEvent"),t={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};o.initCustomEvent("pswpTap",!0,!0,t),e.target.dispatchEvent(o)}var dn,R,mn={};N("Tap",{publicMethods:{initTap:function(){r("firstTouchStart",f.onTapStart),r("touchRelease",f.onTapRelease),r("destroy",function(){mn={},dn=null})},onTapStart:function(e){1<e.length&&(clearTimeout(dn),dn=null)},onTapRelease:function(e,t){var n,o,i;!t||De||ke||at||(n=t,dn&&(clearTimeout(dn),dn=null,o=n,i=mn,Math.abs(o.x-i.x)<q)&&Math.abs(o.y-i.y)<q?I("doubleTap",n):"mouse"===t.type?un(e,t,"mouse"):"BUTTON"===e.target.tagName.toUpperCase()||p.hasClass(e.target,"pswp__single-tap")?un(e,t):(k(mn,n),dn=setTimeout(function(){un(e,t),dn=null},300)))}}}),N("DesktopZoom",{publicMethods:{initDesktopZoom:function(){ye||(i?r("mouseUsed",function(){f.setupDesktopZoom()}):f.setupDesktopZoom(!0))},setupDesktopZoom:function(e){R={};var t="wheel mousewheel DOMMouseScroll";r("bindEvents",function(){p.bind(m,t,f.handleMouseWheel)}),r("unbindEvents",function(){R&&p.unbind(m,t,f.handleMouseWheel)}),f.mouseZoomedIn=!1;function n(){f.mouseZoomedIn&&(p.removeClass(m,"pswp--zoomed-in"),f.mouseZoomedIn=!1),v<1?p.addClass(m,"pswp--zoom-allowed"):p.removeClass(m,"pswp--zoom-allowed"),i()}var o,i=function(){o&&(p.removeClass(m,"pswp--dragging"),o=!1)};r("resize",n),r("afterChange",n),r("pointerDown",function(){f.mouseZoomedIn&&(o=!0,p.addClass(m,"pswp--dragging"))}),r("pointerUp",i),e||n()},handleMouseWheel:function(e){if(v<=f.currItem.fitRatio)return h.modal&&(!h.closeOnScroll||at||l?e.preventDefault():me&&2<Math.abs(e.deltaY)&&($=!0,f.close())),!0;if(e.stopPropagation(),R.x=0,"deltaX"in e)1===e.deltaMode?(R.x=18*e.deltaX,R.y=18*e.deltaY):(R.x=e.deltaX,R.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(R.x=-.16*e.wheelDeltaX),e.wheelDeltaY?R.y=-.16*e.wheelDeltaY:R.y=-.16*e.wheelDelta;else{if(!("detail"in e))return;R.y=e.detail}ot(v,!0);var t=w.x-R.x,n=w.y-R.y;(h.modal||t<=d.min.x&&t>=d.max.x&&n<=d.min.y&&n>=d.max.y)&&e.preventDefault(),f.panTo(t,n)},toggleDesktopZoom:function(e){e=e||{x:x.x/2+We.x,y:x.y/2+We.y};var t=h.getDoubleTapZoom(!0,f.currItem),n=v===t;f.mouseZoomedIn=!n,f.zoomTo(n?f.currItem.initialZoomLevel:t,e,333),p[(n?"remove":"add")+"Class"](m,"pswp--zoomed-in")}}});function pn(){hn&&clearTimeout(hn),vn&&clearTimeout(vn)}function fn(){var e=Sn(),t={};if(!(e.length<5)){var n,o=e.split("&");for(r=0;r<o.length;r++)!o[r]||(n=o[r].split("=")).length<2||(t[n[0]]=n[1]);if(h.galleryPIDs){for(var i=t.pid,r=t.pid=0;r<$t.length;r++)if($t[r].pid===i){t.pid=r;break}}else t.pid=parseInt(t.pid,10)-1;t.pid<0&&(t.pid=0)}return t}var hn,gn,vn,yn,wn,xn,n,bn,En,In,P,Cn,Tn={history:!0,galleryUID:1},Sn=function(){return P.hash.substring(1)},kn=function(){var e,t;vn&&clearTimeout(vn),at||l?vn=setTimeout(kn,500):(yn?clearTimeout(gn):yn=!0,t=g+1,(e=en(g)).hasOwnProperty("pid")&&(t=e.pid),e=n+"&gid="+h.galleryUID+"&pid="+t,bn||-1===P.hash.indexOf(e)&&(In=!0),t=P.href.split("#")[0]+"#"+e,Cn?"#"+e!==window.location.hash&&history[bn?"replaceState":"pushState"]("",document.title,t):bn?P.replace(t):P.hash=e,bn=!0,gn=setTimeout(function(){yn=!1},60))};N("History",{publicMethods:{initHistory:function(){var e,t;p.extend(h,Tn,!0),h.history&&(P=window.location,bn=En=In=!1,n=Sn(),Cn="pushState"in history,-1<n.indexOf("gid=")&&(n=(n=n.split("&gid=")[0]).split("?gid=")[0]),r("afterChange",f.updateURL),r("unbindEvents",function(){p.unbind(window,"hashchange",f.onHashChange)}),e=function(){xn=!0,En||(In?history.back():n?P.hash=n:Cn?history.pushState("",document.title,P.pathname+P.search):P.hash=""),pn()},r("unbindEvents",function(){$&&e()}),r("destroy",function(){xn||e()}),r("firstUpdate",function(){g=fn().pid}),-1<(t=n.indexOf("pid="))&&"&"===(n=n.substring(0,t)).slice(-1)&&(n=n.slice(0,-1)),setTimeout(function(){V&&p.bind(window,"hashchange",f.onHashChange)},40))},onHashChange:function(){return Sn()===n?(En=!0,void f.close()):void(yn||(wn=!0,f.goTo(fn().pid),wn=!1))},updateURL:function(){pn(),wn||(bn?hn=setTimeout(kn,800):kn())}}}),p.extend(f,z)}}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).reframe=t()}(this,function(){"use strict";function t(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var o=Array(e),i=0,t=0;t<n;t++)for(var r=arguments[t],a=0,s=r.length;a<s;a++,i++)o[i]=r[a];return o}return function(e,i){return void 0===i&&(i="js-reframe"),("string"==typeof e?t(document.querySelectorAll(e)):"length"in e?t(e):[e]).forEach(function(e){var t,n,o;-1!==e.className.split(" ").indexOf(i)||-1<e.style.width.indexOf("%")||(t=e.getAttribute("height")||e.offsetHeight,n=e.getAttribute("width")||e.offsetWidth,t=("string"==typeof t?parseInt(t):t)/("string"==typeof n?parseInt(n):n)*100,(n=document.createElement("div")).className=i,(o=n.style).position="relative",o.width="100%",o.paddingTop=t+"%",(o=e.style).position="absolute",o.width="100%",o.height="100%",o.left="0",o.top="0",null!=(t=e.parentNode)&&t.insertBefore(n,e),null!=(o=e.parentNode)&&o.removeChild(e),n.appendChild(e))})}}),function(){const e=document.querySelector(".gh-navigation");var t=e.querySelector(".gh-burger");t&&t.addEventListener("click",function(){e.classList.contains("is-open")?(e.classList.remove("is-open"),document.documentElement.style.overflowY=null):(e.classList.add("is-open"),document.documentElement.style.overflowY="hidden")})}(),lightbox(".kg-image-card > .kg-image[width][height], .kg-gallery-image > img"),reframe(document.querySelectorAll(['.gh-content iframe[src*="youtube.com"]','.gh-content iframe[src*="youtube-nocookie.com"]','.gh-content iframe[src*="player.vimeo.com"]','.gh-content iframe[src*="kickstarter.com"][src*="video.html"]',".gh-content object",".gh-content embed"].join(","))),dropdown(),document.body.classList.contains("home-template")||document.body.classList.contains("post-template")||pagination(),document.querySelectorAll(".gh-content > table:not(.gist table)").forEach(function(e){var t=document.createElement("div");t.className="gh-table",e.parentNode.insertBefore(t,e),t.appendChild(e)});
//# sourceMappingURL=source.js.map
{"name": "@keyvhq/memoize", "description": "Memoize any function using Keyv as storage backend.", "homepage": "https://keyv.js.org", "version": "2.0.3", "main": "src/index.js", "author": {"email": "<EMAIL>", "name": "microlink.io", "url": "https://microlink.io"}, "repository": {"directory": "packages/memo", "type": "git", "url": "git+https://github.com/microlinkhq/keyv.git"}, "bugs": {"url": "https://github.com/microlinkhq/keyvhq/issues"}, "keywords": ["cache", "key", "keyv", "memo", "memoize", "store", "ttl", "value"], "dependencies": {"@keyvhq/core": "^2.0.0", "mimic-fn": "~3.0.0"}, "devDependencies": {"ava": "latest"}, "engines": {"node": ">= 16"}, "files": ["src"], "scripts": {"test": "ava"}, "license": "MIT", "publishConfig": {"access": "public"}, "gitHead": "34f1a39300d53fee66b71137882811e8a7213686"}
{"name": "@keyvhq/core", "description": "Simple key-value storage with support for multiple backends", "homepage": "https://keyv.js.org", "version": "2.1.1", "types": "./src/index.d.ts", "main": "src/index.js", "author": {"email": "<EMAIL>", "name": "microlink.io", "url": "https://microlink.io"}, "repository": {"directory": "packages/keyv", "type": "git", "url": "git+https://github.com/microlinkhq/keyvhq.git"}, "bugs": {"url": "https://github.com/microlinkhq/keyvhq/issues"}, "keywords": ["cache", "key", "keyv", "store", "ttl", "value"], "dependencies": {"json-buffer": "~3.0.1"}, "devDependencies": {"@keyvhq/test-suite": "latest", "@types/node": "latest", "ava": "5", "timekeeper": "latest", "tsd": "latest", "typescript": "latest"}, "engines": {"node": ">= 16"}, "files": ["src"], "scripts": {"lint": "tsd", "pretest": "npm run lint", "test": "ava"}, "license": "MIT", "publishConfig": {"access": "public"}, "tsd": {"directory": "test"}, "gitHead": "8712c7b57c3b9614a67182fa14d60c63bb27e936"}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */

import type {
  BaseSelection,
  LexicalEditor,
  LexicalNode,
  EditorState,
  EditorThemeClasses,
} from 'lexical';

export type FindCachedParentDOMNode = (
  node: Node,
  searchFn: FindCachedParentDOMNodeSearchFn,
) => null | Node;
export type FindCachedParentDOMNodeSearchFn = (node: Node) => boolean;

declare export function $generateHtmlFromNodes(
  editor: LexicalEditor,
  selection?: BaseSelection | null,
): string;

declare export function $generateNodesFromDOM(
  editor: LexicalEditor,
  dom: Document,
): Array<LexicalNode>;

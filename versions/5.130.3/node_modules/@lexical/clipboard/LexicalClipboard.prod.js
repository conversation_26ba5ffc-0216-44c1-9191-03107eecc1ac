/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var f=require("@lexical/html"),m=require("@lexical/selection"),q=require("@lexical/utils"),r=require("lexical");function t(a){let b=new URLSearchParams;b.append("code",a);for(let c=1;c<arguments.length;c++)b.append("v",arguments[c]);throw Error(`Minified Lexical error #${a}; visit https://lexical.dev/docs/error?${b} for the full message or `+"use the non-minified dev environment for full errors and additional helpful warnings.");}
let u="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;function v(a){let b=r.$getSelection();if(null==b)throw Error("Expected valid LexicalSelection");return r.$isRangeSelection(b)&&b.isCollapsed()||0===b.getNodes().length?"":f.$generateHtmlFromNodes(a,b)}
function w(a){let b=r.$getSelection();if(null==b)throw Error("Expected valid LexicalSelection");return r.$isRangeSelection(b)&&b.isCollapsed()||0===b.getNodes().length?null:JSON.stringify(x(a,b))}function y(a,b,c){a.dispatchCommand(r.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND,{nodes:b,selection:c})||c.insertNodes(b)}
function z(a,b,c,d=[]){let e=null!==b?c.isSelected(b):!0,h=r.$isElementNode(c)&&c.excludeFromCopy("html");var g=c;if(null!==b){var k=m.$cloneWithProperties(c);g=k=r.$isTextNode(k)&&null!==b?m.$sliceSelectedTextNodeContent(b,k):k}let n=r.$isElementNode(g)?g.getChildren():[];var l=g;k=l.exportJSON();var p=l.constructor;k.type!==p.getType()&&t(58,p.name);r.$isElementNode(l)&&(Array.isArray(k.children)||t(59,p.name));r.$isTextNode(g)&&(g=g.__text,0<g.length?k.text=g:e=!1);for(g=0;g<n.length;g++)l=n[g],
p=z(a,b,l,k.children),!e&&r.$isElementNode(c)&&p&&c.extractWithChild(l,b,"clone")&&(e=!0);if(e&&!h)d.push(k);else if(Array.isArray(k.children))for(a=0;a<k.children.length;a++)d.push(k.children[a]);return e}function x(a,b){let c=[],d=r.$getRoot().getChildren();for(let e=0;e<d.length;e++)z(a,b,d[e],c);return{namespace:a._config.namespace,nodes:c}}function A(a){let b=[];for(let c=0;c<a.length;c++){let d=r.$parseSerializedNode(a[c]);r.$isTextNode(d)&&m.$addNodeStyle(d);b.push(d)}return b}let B=null;
function C(a,b){var c=u?(a._window||window).getSelection():null;if(!c)return!1;var d=c.anchorNode;c=c.focusNode;if(null!==d&&null!==c&&!r.isSelectionWithinEditor(a,d,c))return!1;b.preventDefault();b=b.clipboardData;d=r.$getSelection();if(null===b||null===d)return!1;c=v(a);a=w(a);let e="";null!==d&&(e=d.getTextContent());null!==c&&b.setData("text/html",c);null!==a&&b.setData("application/x-lexical-editor",a);b.setData("text/plain",e);return!0}exports.$generateJSONFromSelectedNodes=x;
exports.$generateNodesFromSerializedNodes=A;exports.$getHtmlContent=v;exports.$getLexicalContent=w;exports.$insertDataTransferForPlainText=function(a,b){a=a.getData("text/plain")||a.getData("text/uri-list");null!=a&&b.insertRawText(a)};
exports.$insertDataTransferForRichText=function(a,b,c){var d=a.getData("application/x-lexical-editor");if(d)try{let h=JSON.parse(d);if(h.namespace===c._config.namespace&&Array.isArray(h.nodes)){let g=A(h.nodes);return y(c,g,b)}}catch(h){}if(d=a.getData("text/html"))try{var e=(new DOMParser).parseFromString(d,"text/html");let h=f.$generateNodesFromDOM(c,e);return y(c,h,b)}catch(h){}a=a.getData("text/plain")||a.getData("text/uri-list");if(null!=a)if(r.$isRangeSelection(b))for(a=a.split(/(\r?\n|\t)/),
""===a[a.length-1]&&a.pop(),c=0;c<a.length;c++)e=a[c],"\n"===e||"\r\n"===e?b.insertParagraph():"\t"===e?b.insertNodes([r.$createTabNode()]):b.insertText(e);else b.insertRawText(a)};exports.$insertGeneratedNodes=y;
exports.copyToClipboard=async function(a,b){if(null!==B)return!1;if(null!==b)return new Promise(g=>{a.update(()=>{g(C(a,b))})});var c=a.getRootElement();let d=null==a._window?window.document:a._window.document,e=u?(a._window||window).getSelection():null;if(null===c||null===e)return!1;let h=d.createElement("span");h.style.cssText="position: fixed; top: -1000px;";h.append(d.createTextNode("#"));c.append(h);c=new Range;c.setStart(h,0);c.setEnd(h,1);e.removeAllRanges();e.addRange(c);return new Promise(g=>
{let k=a.registerCommand(r.COPY_COMMAND,n=>{q.objectKlassEquals(n,ClipboardEvent)&&(k(),null!==B&&(window.clearTimeout(B),B=null),g(C(a,n)));return!0},r.COMMAND_PRIORITY_CRITICAL);B=window.setTimeout(()=>{k();B=null;g(!1)},50);d.execCommand("copy");h.remove()})}

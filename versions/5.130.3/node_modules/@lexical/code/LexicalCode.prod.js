/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var d=require("prismjs");require("prismjs/components/prism-clike");require("prismjs/components/prism-javascript");require("prismjs/components/prism-markup");require("prismjs/components/prism-markdown");require("prismjs/components/prism-c");require("prismjs/components/prism-css");require("prismjs/components/prism-objectivec");require("prismjs/components/prism-sql");require("prismjs/components/prism-python");require("prismjs/components/prism-rust");require("prismjs/components/prism-swift");
require("prismjs/components/prism-typescript");require("prismjs/components/prism-java");require("prismjs/components/prism-cpp");var m=require("@lexical/utils"),q=require("lexical");let v=a=>null!=a&&d.languages.hasOwnProperty(a)?a:void 0;function x(a,b){for(let c of a.childNodes){if(m.isHTMLElement(c)&&c.tagName===b)return!0;x(c,b)}return!1}
class y extends q.ElementNode{static getType(){return"code"}static clone(a){return new y(a.__language,a.__key)}constructor(a,b){super(b);this.__language=v(a)}createDOM(a){let b=document.createElement("code");m.addClassNamesToElement(b,a.theme.code);b.setAttribute("spellcheck","false");(a=this.getLanguage())&&b.setAttribute("data-highlight-language",a);return b}updateDOM(a,b){let c=this.__language;a=a.__language;c?c!==a&&b.setAttribute("data-highlight-language",c):a&&b.removeAttribute("data-highlight-language");
return!1}exportDOM(){let a=document.createElement("pre");a.setAttribute("spellcheck","false");let b=this.getLanguage();b&&a.setAttribute("data-highlight-language",b);return{element:a}}static importDOM(){return{code:a=>null!=a.textContent&&(/\r?\n/.test(a.textContent)||x(a,"BR"))?{conversion:z,priority:1}:null,div:()=>({conversion:aa,priority:1}),pre:()=>({conversion:z,priority:0}),table:a=>A(a)?{conversion:ba,priority:3}:null,td:a=>{let b=a.closest("table");return a.classList.contains("js-file-line")?
{conversion:ca,priority:3}:b&&A(b)?{conversion:B,priority:3}:null},tr:a=>(a=a.closest("table"))&&A(a)?{conversion:B,priority:3}:null}}static importJSON(a){let b=C(a.language);b.setFormat(a.format);b.setIndent(a.indent);b.setDirection(a.direction);return b}exportJSON(){return{...super.exportJSON(),language:this.getLanguage(),type:"code",version:1}}insertNewAfter(a,b=!0){var c=this.getChildren(),e=c.length;if(2<=e&&"\n"===c[e-1].getTextContent()&&"\n"===c[e-2].getTextContent()&&a.isCollapsed()&&a.anchor.key===
this.__key&&a.anchor.offset===e)return c[e-1].remove(),c[e-2].remove(),a=q.$createParagraphNode(),this.insertAfter(a,b),a;let {anchor:f,focus:g}=a;b=(f.isBefore(g)?f:g).getNode();if(q.$isTextNode(b)){e=D(b);for(c=[];;)if(q.$isTabNode(e))c.push(q.$createTabNode()),e=e.getNextSibling();else if(E(e)){for(var h=0,k=e.getTextContent(),l=e.getTextContentSize();h<l&&" "===k[h];h++);0!==h&&c.push(F(" ".repeat(h)));if(h!==l)break;e=e.getNextSibling()}else break;e=b.splitText(f.offset)[0];h=0===f.offset?0:
1;h=e.getIndexWithinParent()+h;k=b.getParentOrThrow();l=[q.$createLineBreakNode(),...c];k.splice(h,0,l);(c=c[c.length-1])?c.select():0===f.offset?e.selectPrevious():e.getNextSibling().selectNext(0,0)}G(b)&&({offset:a}=a.anchor,b.splice(a,0,[q.$createLineBreakNode()]),b.select(a+1,a+1));return null}canIndent(){return!1}collapseAtStart(){let a=q.$createParagraphNode();this.getChildren().forEach(b=>a.append(b));this.replace(a);return!0}setLanguage(a){this.getWritable().__language=v(a)}getLanguage(){return this.getLatest().__language}}
function C(a){return q.$applyNodeReplacement(new y(a))}function G(a){return a instanceof y}function z(a){let b;m.isHTMLElement(a)&&(b=a.getAttribute("data-highlight-language"));return{node:C(b)}}function aa(a){let b=null!==a.style.fontFamily.match("monospace");return b||da(a)?{after:c=>{let e=a.parentNode;null!=e&&a!==e.lastChild&&c.push(q.$createLineBreakNode());return c},node:b?C():null}:{node:null}}function ba(){return{node:C()}}function B(){return{node:null}}
function ca(a){return{after:b=>{a.parentNode&&a.parentNode.nextSibling&&b.push(q.$createLineBreakNode());return b},node:null}}function da(a){for(a=a.parentElement;null!==a;){if(null!==a.style.fontFamily.match("monospace"))return!0;a=a.parentElement}return!1}function A(a){return a.classList.contains("js-file-line-container")}
let H={c:"C",clike:"C-like",cpp:"C++",css:"CSS",html:"HTML",java:"Java",js:"JavaScript",markdown:"Markdown",objc:"Objective-C",plain:"Plain Text",py:"Python",rust:"Rust",sql:"SQL",swift:"Swift",typescript:"TypeScript",xml:"XML"},I={cpp:"cpp",java:"java",javascript:"js",md:"markdown",plaintext:"plain",python:"py",text:"plain",ts:"typescript"};function K(a){return I[a]||a}
class L extends q.TextNode{constructor(a,b,c){super(a,c);this.__highlightType=b}static getType(){return"code-highlight"}static clone(a){return new L(a.__text,a.__highlightType||void 0,a.__key)}getHighlightType(){return this.getLatest().__highlightType}canHaveFormat(){return!1}createDOM(a){let b=super.createDOM(a);a=M(a.theme,this.__highlightType);m.addClassNamesToElement(b,a);return b}updateDOM(a,b,c){let e=super.updateDOM(a,b,c);a=M(c.theme,a.__highlightType);c=M(c.theme,this.__highlightType);a!==
c&&(a&&m.removeClassNamesFromElement(b,a),c&&m.addClassNamesToElement(b,c));return e}static importJSON(a){let b=F(a.text,a.highlightType);b.setFormat(a.format);b.setDetail(a.detail);b.setMode(a.mode);b.setStyle(a.style);return b}exportJSON(){return{...super.exportJSON(),highlightType:this.getHighlightType(),type:"code-highlight",version:1}}setFormat(){return this}isParentRequired(){return!0}createParentElementNode(){return C()}}function M(a,b){return b&&a&&a.codeHighlight&&a.codeHighlight[b]}
function F(a,b){return q.$applyNodeReplacement(new L(a,b))}function E(a){return a instanceof L}function D(a){let b=a;for(;E(a)||q.$isTabNode(a);)b=a,a=a.getPreviousSibling();return b}function N(a){let b=a;for(;E(a)||q.$isTabNode(a);)b=a,a=a.getNextSibling();return b}let O={defaultLanguage:"javascript",tokenize(a,b){return d.tokenize(a,d.languages[b||""]||d.languages[this.defaultLanguage])}};
function P(a,b){let c=null;var e=null,f=a;let g=b,h=a.getTextContent();for(;;){if(0===g){f=f.getPreviousSibling();if(null===f)break;if(!(E(f)||q.$isTabNode(f)||q.$isLineBreakNode(f)))throw Error("Expected a valid Code Node: CodeHighlightNode, TabNode, LineBreakNode");if(q.$isLineBreakNode(f)){c={node:f,offset:1};break}g=Math.max(0,f.getTextContentSize()-1);h=f.getTextContent()}else g--;let k=h[g];E(f)&&" "!==k&&(e={node:f,offset:g})}if(null!==e)return e;e=null;b<a.getTextContentSize()?E(a)&&(e=a.getTextContent()[b]):
(f=a.getNextSibling(),E(f)&&(e=f.getTextContent()[0]));if(null!==e&&" "!==e)return c;a:for(e=a,f=a.getTextContent(),a=a.getTextContentSize();;){if(!E(e)||b===a){e=e.getNextSibling();if(null===e||q.$isLineBreakNode(e)){a=null;break a}E(e)&&(b=0,f=e.getTextContent(),a=e.getTextContentSize())}if(E(e)){if(" "!==f[b]){a={node:e,offset:b};break a}b++}}return null!==a?a:c}function Q(a){a=N(a);if(q.$isLineBreakNode(a))throw Error("Unexpected lineBreakNode in getEndOfCodeInLine");return a}
function R(a,b,c){let e=a.getParent();G(e)?S(e,b,c):E(a)&&a.replace(q.$createTextNode(a.__text))}let T=new Set;
function S(a,b,c){let e=a.getKey();T.has(e)||(T.add(e),void 0===a.getLanguage()&&a.setLanguage(c.defaultLanguage),b.update(()=>{ea(e,()=>{var f=q.$getNodeByKey(e);if(!G(f)||!f.isAttached())return!1;var g=f.getTextContent();g=c.tokenize(g,f.getLanguage()||c.defaultLanguage);g=U(g);var h=f.getChildren();for(f=0;f<h.length&&V(h[f],g[f]);)f++;var k=h.length;let l=g.length,r=Math.min(k,l)-f,n=0;for(;n<r;)if(n++,!V(h[k-n],g[l-n])){n--;break}h=f;k-=n;g=g.slice(f,l-n);let {from:p,to:w,nodesForReplacement:u}=
{from:h,nodesForReplacement:g,to:k};return p!==w||u.length?(a.splice(p,w-p,u),!0):!1})},{onUpdate:()=>{T.delete(e)},skipTransforms:!0}))}
function U(a,b){let c=[];for(let e of a)if("string"===typeof e){a=e.split(/(\n|\t)/);let f=a.length;for(let g=0;g<f;g++){let h=a[g];"\n"===h||"\r\n"===h?c.push(q.$createLineBreakNode()):"\t"===h?c.push(q.$createTabNode()):0<h.length&&c.push(F(h,b))}}else({content:a}=e),"string"===typeof a?c.push(...U([a],e.type)):Array.isArray(a)&&c.push(...U(a,e.type));return c}
function ea(a,b){a=q.$getNodeByKey(a);if(G(a)&&a.isAttached()){var c=q.$getSelection();if(q.$isRangeSelection(c)){c=c.anchor;var e=c.offset,f="element"===c.type&&q.$isLineBreakNode(a.getChildAtIndex(c.offset-1)),g=0;if(!f){let h=c.getNode();g=e+h.getPreviousSiblings().reduce((k,l)=>k+l.getTextContentSize(),0)}b()&&(f?c.getNode().select(e,e):a.getChildren().some(h=>{let k=q.$isTextNode(h);if(k||q.$isLineBreakNode(h)){let l=h.getTextContentSize();if(k&&l>=g)return h.select(g,g),!0;g-=l}return!1}))}else b()}}
function V(a,b){return E(a)&&E(b)&&a.__text===b.__text&&a.__highlightType===b.__highlightType||q.$isTabNode(a)&&q.$isTabNode(b)||q.$isLineBreakNode(a)&&q.$isLineBreakNode(b)}function W(a){if(!q.$isRangeSelection(a))return!1;var b=a.anchor.getNode();a=a.focus.getNode();if(b.is(a)&&G(b))return!0;b=b.getParent();return G(b)&&b.is(a.getParent())}
function X(a){a=a.getNodes();let b=[[]];if(1===a.length&&G(a[0]))return b;let c=b[0];for(let e=0;e<a.length;e++){let f=a[e];if(!(E(f)||q.$isTabNode(f)||q.$isLineBreakNode(f)))throw Error("Expected selection to be inside CodeBlock and consisting of CodeHighlightNode, TabNode and LineBreakNode");q.$isLineBreakNode(f)?0!==e&&0<c.length&&(c=[],b.push(c)):c.push(f)}return b}
function fa(a){var b=q.$getSelection();if(!q.$isRangeSelection(b)||!W(b))return null;let c=a?q.OUTDENT_CONTENT_COMMAND:q.INDENT_CONTENT_COMMAND;a=a?q.OUTDENT_CONTENT_COMMAND:q.INSERT_TAB_COMMAND;if(1<X(b).length)return c;var e=b.getNodes()[0];if(!(G(e)||E(e)||q.$isTabNode(e)||q.$isLineBreakNode(e)))throw Error("Expected selection firstNode to be CodeHighlightNode or TabNode");if(G(e))return c;let f=D(e);e=N(e);var g=b.anchor;let h=b.focus;h.isBefore(g)?b=h:(b=g,g=h);return null!==f&&null!==e&&b.key===
f.getKey()&&0===b.offset&&g.key===e.getKey()&&g.offset===e.getTextContentSize()?c:a}
function Y(a){var b=q.$getSelection();if(!q.$isRangeSelection(b)||!W(b))return!1;var c=X(b);let e=c.length;if(1<c.length){for(b=0;b<e;b++){var f=c[b];0<f.length&&(f=f[0],0===b&&(f=D(f)),null!==f&&(a===q.INDENT_CONTENT_COMMAND?f.insertBefore(q.$createTabNode()):q.$isTabNode(f)&&f.remove()))}return!0}c=b.getNodes()[0];if(!(G(c)||E(c)||q.$isTabNode(c)||q.$isLineBreakNode(c)))throw Error("Expected selection firstNode to be CodeHighlightNode or CodeTabNode");if(G(c))return a===q.INDENT_CONTENT_COMMAND&&
b.insertNodes([q.$createTabNode()]),!0;c=D(c);if(null===c)throw Error("Expected getFirstCodeNodeOfLine to return a valid Code Node");a===q.INDENT_CONTENT_COMMAND?q.$isLineBreakNode(c)?c.insertAfter(q.$createTabNode()):c.insertBefore(q.$createTabNode()):q.$isTabNode(c)&&c.remove();return!0}
function Z(a,b){let c=q.$getSelection();if(!q.$isRangeSelection(c))return!1;let {anchor:e,focus:f}=c,g=e.offset,h=f.offset,k=e.getNode(),l=f.getNode();var r=a===q.KEY_ARROW_UP_COMMAND;if(!W(c)||!E(k)&&!q.$isTabNode(k)||!E(l)&&!q.$isTabNode(l))return!1;if(!b.altKey){if(c.isCollapsed())if(a=k.getParentOrThrow(),r&&0===g&&null===k.getPreviousSibling()){if(null===a.getPreviousSibling())return a.selectPrevious(),b.preventDefault(),!0}else if(!r&&g===k.getTextContentSize()&&null===k.getNextSibling()&&null===
a.getNextSibling())return a.selectNext(),b.preventDefault(),!0;return!1}let n;if(k.isBefore(l)){var p=D(k);n=N(l)}else p=D(l),n=N(k);if(null==p||null==n)return!1;let w=p.getNodesBetween(n);for(let t=0;t<w.length;t++){let J=w[t];if(!E(J)&&!q.$isTabNode(J)&&!q.$isLineBreakNode(J))return!1}b.preventDefault();b.stopPropagation();b=r?p.getPreviousSibling():n.getNextSibling();if(!q.$isLineBreakNode(b))return!0;p=r?b.getPreviousSibling():b.getNextSibling();if(null==p)return!0;r=E(p)||q.$isTabNode(p)||q.$isLineBreakNode(p)?
r?D(p):N(p):null;let u=null!=r?r:p;b.remove();w.forEach(t=>t.remove());a===q.KEY_ARROW_UP_COMMAND?(w.forEach(t=>u.insertBefore(t)),u.insertBefore(b)):(u.insertAfter(b),u=b,w.forEach(t=>{u.insertAfter(t);u=t}));c.setTextNodeRange(k,g,l,h);return!0}
function ha(a,b){let c=q.$getSelection();if(!q.$isRangeSelection(c))return!1;let {anchor:e,focus:f}=c;var g=e.getNode();let h=f.getNode();a=a===q.MOVE_TO_START;if(!E(g)&&!q.$isTabNode(g)||!E(h)&&!q.$isTabNode(h))return!1;if(a)if(g=P(h,f.offset),null!==g){let {node:k,offset:l}=g;q.$isLineBreakNode(k)?k.selectNext(0,0):c.setTextNodeRange(k,l,k,l)}else h.getParentOrThrow().selectStart();else Q(h).select();b.preventDefault();b.stopPropagation();return!0}exports.$createCodeHighlightNode=F;
exports.$createCodeNode=C;exports.$isCodeHighlightNode=E;exports.$isCodeNode=G;exports.CODE_LANGUAGE_FRIENDLY_NAME_MAP=H;exports.CODE_LANGUAGE_MAP=I;exports.CodeHighlightNode=L;exports.CodeNode=y;exports.DEFAULT_CODE_LANGUAGE="javascript";exports.PrismTokenizer=O;exports.getCodeLanguages=()=>Object.keys(d.languages).filter(a=>"function"!==typeof d.languages[a]).sort();exports.getDefaultCodeLanguage=()=>"javascript";exports.getEndOfCodeInLine=Q;exports.getFirstCodeNodeOfLine=D;
exports.getLanguageFriendlyName=function(a){a=K(a);return H[a]||a};exports.getLastCodeNodeOfLine=N;exports.getStartOfCodeInLine=P;exports.normalizeCodeLang=K;
exports.registerCodeHighlighting=function(a,b){if(!a.hasNodes([y,L]))throw Error("CodeHighlightPlugin: CodeNode or CodeHighlightNode not registered on editor");null==b&&(b=O);return m.mergeRegister(a.registerMutationListener(y,c=>{a.update(()=>{for(let [g,h]of c)if("destroyed"!==h){var e=q.$getNodeByKey(g);if(null!==e)a:{var f=e;e=a.getElementByKey(f.getKey());if(null===e)break a;f=f.getChildren();let k=f.length;if(k===e.__cachedChildrenLength)break a;e.__cachedChildrenLength=k;let l="1",r=1;for(let n=
0;n<k;n++)q.$isLineBreakNode(f[n])&&(l+="\n"+ ++r);e.setAttribute("data-gutter",l)}}})}),a.registerNodeTransform(y,c=>S(c,a,b)),a.registerNodeTransform(q.TextNode,c=>R(c,a,b)),a.registerNodeTransform(L,c=>R(c,a,b)),a.registerCommand(q.KEY_TAB_COMMAND,c=>{let e=fa(c.shiftKey);if(null===e)return!1;c.preventDefault();a.dispatchCommand(e,void 0);return!0},q.COMMAND_PRIORITY_LOW),a.registerCommand(q.INSERT_TAB_COMMAND,()=>{let c=q.$getSelection();if(!W(c))return!1;q.$insertNodes([q.$createTabNode()]);
return!0},q.COMMAND_PRIORITY_LOW),a.registerCommand(q.INDENT_CONTENT_COMMAND,()=>Y(q.INDENT_CONTENT_COMMAND),q.COMMAND_PRIORITY_LOW),a.registerCommand(q.OUTDENT_CONTENT_COMMAND,()=>Y(q.OUTDENT_CONTENT_COMMAND),q.COMMAND_PRIORITY_LOW),a.registerCommand(q.KEY_ARROW_UP_COMMAND,c=>Z(q.KEY_ARROW_UP_COMMAND,c),q.COMMAND_PRIORITY_LOW),a.registerCommand(q.KEY_ARROW_DOWN_COMMAND,c=>Z(q.KEY_ARROW_DOWN_COMMAND,c),q.COMMAND_PRIORITY_LOW),a.registerCommand(q.MOVE_TO_END,c=>ha(q.MOVE_TO_END,c),q.COMMAND_PRIORITY_LOW),
a.registerCommand(q.MOVE_TO_START,c=>ha(q.MOVE_TO_START,c),q.COMMAND_PRIORITY_LOW))}

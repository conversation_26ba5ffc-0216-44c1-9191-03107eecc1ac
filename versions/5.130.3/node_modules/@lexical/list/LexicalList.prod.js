/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var h=require("lexical"),k=require("@lexical/utils");function l(a){let b=new URLSearchParams;b.append("code",a);for(let c=1;c<arguments.length;c++)b.append("v",arguments[c]);throw Error(`Minified Lexical error #${a}; visit https://lexical.dev/docs/error?${b} for the full message or `+"use the non-minified dev environment for full errors and additional helpful warnings.");}
function n(a){let b=1;for(a=a.getParent();null!=a;){if(p(a)){a=a.getParent();if(q(a)){b++;a=a.getParent();continue}l(40)}break}return b}function r(a){a=a.getParent();q(a)||l(40);let b=a;for(;null!==b;)b=b.getParent(),q(b)&&(a=b);return a}function t(a){let b=[];a=a.getChildren().filter(p);for(let c=0;c<a.length;c++){let d=a[c],e=d.getFirstChild();q(e)?b=b.concat(t(e)):b.push(d)}return b}function u(a){return p(a)&&q(a.getFirstChild())}
function v(a){for(;null==a.getNextSibling()&&null==a.getPreviousSibling();){let b=a.getParent();if(null==b||!p(a)&&!q(a))break;a=b}a.remove()}function w(a){return y().append(a)}function z(a,b){return p(a)&&(0===b.length||1===b.length&&a.is(b[0])&&0===a.getChildrenSize())}function C(a,b){a.splice(a.getChildrenSize(),0,b)}
function D(a,b){if(q(a))return a;let c=a.getPreviousSibling(),d=a.getNextSibling(),e=y();e.setFormat(a.getFormatType());e.setIndent(a.getIndent());C(e,a.getChildren());if(q(c)&&b===c.getListType())return c.append(e),a.remove(),q(d)&&b===d.getListType()&&(C(c,d.getChildren()),d.remove()),c;if(q(d)&&b===d.getListType())return d.getFirstChildOrThrow().insertBefore(e),a.remove(),d;b=E(b);b.append(e);a.replace(b);F(b);return b}
function G(a,b){var c=a.getLastChild();let d=b.getFirstChild();c&&d&&u(c)&&u(d)&&(G(c.getFirstChild(),d.getFirstChild()),d.remove());c=b.getChildren();0<c.length&&(a.append(...c),F(a));b.remove()}function F(a,b){a=b||a.getChildren();if(void 0!==a)for(b=0;b<a.length;b++){let f=a[b];if(p(f)){let g=f.getValue();var c=f,d=c.getParent(),e=1;null!=d&&(q(d)?e=d.getStart():l(44));c=c.getPreviousSiblings();for(d=0;d<c.length;d++){let m=c[d];p(m)&&!q(m.getFirstChild())&&e++}g!==e&&f.setValue(e)}}}
function H(a){if(!u(a)){var b=a.getParent(),c=b?b.getParent():void 0,d=c?c.getParent():void 0;if(q(d)&&p(c)&&q(b)){var e=b?b.getFirstChild():void 0,f=b?b.getLastChild():void 0;if(a.is(e))c.insertBefore(a),b.isEmpty()&&c.remove();else if(a.is(f))c.insertAfter(a),b.isEmpty()&&c.remove();else{var g=b.getListType();e=y();let m=E(g);e.append(m);a.getPreviousSiblings().forEach(x=>m.append(x));f=y();g=E(g);f.append(g);C(g,a.getNextSiblings());c.insertBefore(e);c.insertAfter(f);c.replace(a)}F(b);F(d)}}}
class I extends h.ElementNode{static getType(){return"listitem"}static clone(a){return new I(a.__value,a.__checked,a.__key)}constructor(a,b,c){super(c);this.__value=void 0===a?1:a;this.__checked=b}createDOM(a){let b=document.createElement("li"),c=this.getParent();q(c)&&"check"===c.getListType()&&J(b,this,null);b.value=this.__value;K(b,a.theme,this);return b}updateDOM(a,b,c){let d=this.getParent();q(d)&&"check"===d.getListType()&&J(b,this,a);b.value=this.__value;K(b,c.theme,this);return!1}static transform(){return a=>
{let b=a.getParent();q(b)&&(F(b),p(a)||l(144),"check"!==b.getListType()&&null!=a.getChecked()&&a.setChecked(void 0))}}static importDOM(){return{li:()=>({conversion:L,priority:0})}}static importJSON(a){let b=y();b.setChecked(a.checked);b.setValue(a.value);b.setFormat(a.format);b.setDirection(a.direction);return b}exportDOM(a){a=this.createDOM(a._config);a.style.textAlign=this.getFormatType();return{element:a}}exportJSON(){return{...super.exportJSON(),checked:this.getChecked(),type:"listitem",value:this.getValue(),
version:1}}append(...a){for(let b=0;b<a.length;b++){let c=a[b];if(h.$isElementNode(c)&&this.canMergeWith(c)){let d=c.getChildren();this.append(...d);c.remove()}else super.append(c)}return this}replace(a,b){if(p(a))return super.replace(a);this.setIndent(0);let c=this.getParentOrThrow();if(!q(c))return a;if(c.__first===this.getKey())c.insertBefore(a);else if(c.__last===this.getKey())c.insertAfter(a);else{let d=E(c.getListType()),e=this.getNextSibling();for(;e;){let f=e;e=e.getNextSibling();d.append(f)}c.insertAfter(a);
a.insertAfter(d)}b&&(h.$isElementNode(a)||l(139),this.getChildren().forEach(d=>{a.append(d)}));this.remove();0===c.getChildrenSize()&&c.remove();return a}insertAfter(a,b=!0){var c=this.getParentOrThrow();q(c)||l(39);var d=this.getNextSiblings();if(p(a))return b=super.insertAfter(a,b),a=a.getParentOrThrow(),q(a)&&F(a),b;if(q(a)){c=a;a=a.getChildren();for(d=a.length-1;0<=d;d--)c=a[d],this.insertAfter(c,b);return c}c.insertAfter(a,b);if(0!==d.length){let e=E(c.getListType());d.forEach(f=>e.append(f));
a.insertAfter(e,b)}return a}remove(a){let b=this.getPreviousSibling(),c=this.getNextSibling();super.remove(a);b&&c&&u(b)&&u(c)?(G(b.getFirstChild(),c.getFirstChild()),c.remove()):c&&(a=c.getParent(),q(a)&&F(a))}insertNewAfter(a,b=!0){a=y(null==this.__checked?void 0:!1);this.insertAfter(a,b);return a}collapseAtStart(a){let b=h.$createParagraphNode();this.getChildren().forEach(f=>b.append(f));var c=this.getParentOrThrow(),d=c.getParentOrThrow();let e=p(d);1===c.getChildrenSize()?e?(c.remove(),d.select()):
(c.insertBefore(b),c.remove(),c=a.anchor,a=a.focus,d=b.getKey(),"element"===c.type&&c.getNode().is(this)&&c.set(d,c.offset,"element"),"element"===a.type&&a.getNode().is(this)&&a.set(d,a.offset,"element")):(c.insertBefore(b),this.remove());return!0}getValue(){return this.getLatest().__value}setValue(a){this.getWritable().__value=a}getChecked(){return this.getLatest().__checked}setChecked(a){this.getWritable().__checked=a}toggleChecked(){this.setChecked(!this.__checked)}getIndent(){var a=this.getParent();
if(null===a)return this.getLatest().__indent;a=a.getParentOrThrow();let b=0;for(;p(a);)a=a.getParentOrThrow().getParentOrThrow(),b++;return b}setIndent(a){"number"===typeof a&&-1<a||l(117);let b=this.getIndent();for(;b!==a;)if(b<a){a:{var c=new Set;if(u(this)||c.has(this.getKey()))break a;let g=this.getParent();var d=this.getNextSibling(),e=this.getPreviousSibling();if(u(d)&&u(e)){if(e=e.getFirstChild(),q(e)){e.append(this);var f=d.getFirstChild();q(f)&&(f=f.getChildren(),C(e,f),d.remove(),c.add(d.getKey()));
F(e)}}else u(d)?(d=d.getFirstChild(),q(d)&&(c=d.getFirstChild(),null!==c&&c.insertBefore(this),F(d))):u(e)?(d=e.getFirstChild(),q(d)&&(d.append(this),F(d))):q(g)&&(c=y(),f=E(g.getListType()),c.append(f),f.append(this),e?e.insertAfter(c):d?d.insertBefore(c):g.append(c),F(f));q(g)&&F(g)}b++}else H(this),b--;return this}insertBefore(a){if(p(a)){let b=this.getParentOrThrow();if(q(b)){let c=this.getNextSiblings();F(b,c)}}return super.insertBefore(a)}canInsertAfter(a){return p(a)}canReplaceWith(a){return p(a)}canMergeWith(a){return h.$isParagraphNode(a)||
p(a)}extractWithChild(a,b){if(!h.$isRangeSelection(b))return!1;a=b.anchor.getNode();let c=b.focus.getNode();return this.isParentOf(a)&&this.isParentOf(c)&&this.getTextContent().length===b.getTextContent().length}isParentRequired(){return!0}createParentElementNode(){return E("bullet")}}
function K(a,b,c){let d=[],e=[];var f=(b=b.list)?b.listitem:void 0;if(b&&b.nested)var g=b.nested.listitem;void 0!==f&&(f=f.split(" "),d.push(...f));if(b){f=c.getParent();f=q(f)&&"check"===f.getListType();let m=c.getChecked();f&&!m||e.push(b.listitemUnchecked);f&&m||e.push(b.listitemChecked);f&&d.push(m?b.listitemChecked:b.listitemUnchecked)}void 0!==g&&(g=g.split(" "),c.getChildren().some(m=>q(m))?d.push(...g):e.push(...g));0<e.length&&k.removeClassNamesFromElement(a,...e);0<d.length&&k.addClassNamesToElement(a,
...d)}function J(a,b,c){q(b.getFirstChild())?(a.removeAttribute("role"),a.removeAttribute("tabIndex"),a.removeAttribute("aria-checked")):(a.setAttribute("role","checkbox"),a.setAttribute("tabIndex","-1"),c&&b.__checked===c.__checked||a.setAttribute("aria-checked",b.getChecked()?"true":"false"))}function L(a){a=k.isHTMLElement(a)&&"true"===a.getAttribute("aria-checked");return{node:y(a)}}function y(a){return h.$applyNodeReplacement(new I(void 0,a))}function p(a){return a instanceof I}
class M extends h.ElementNode{static getType(){return"list"}static clone(a){return new M(a.__listType||O[a.__tag],a.__start,a.__key)}constructor(a,b,c){super(c);this.__listType=a=O[a]||a;this.__tag="number"===a?"ol":"ul";this.__start=b}getTag(){return this.__tag}setListType(a){let b=this.getWritable();b.__listType=a;b.__tag="number"===a?"ol":"ul"}getListType(){return this.__listType}getStart(){return this.__start}createDOM(a){let b=document.createElement(this.__tag);1!==this.__start&&b.setAttribute("start",
String(this.__start));b.__lexicalListType=this.__listType;P(b,a.theme,this);return b}updateDOM(a,b,c){if(a.__tag!==this.__tag)return!0;P(b,c.theme,this);return!1}static importDOM(){return{ol:()=>({conversion:Q,priority:0}),ul:()=>({conversion:Q,priority:0})}}static importJSON(a){let b=E(a.listType,a.start);b.setFormat(a.format);b.setIndent(a.indent);b.setDirection(a.direction);return b}exportDOM(a){({element:a}=super.exportDOM(a));a&&k.isHTMLElement(a)&&(1!==this.__start&&a.setAttribute("start",String(this.__start)),
"check"===this.__listType&&a.setAttribute("__lexicalListType","check"));return{element:a}}exportJSON(){return{...super.exportJSON(),listType:this.getListType(),start:this.getStart(),tag:this.getTag(),type:"list",version:1}}canBeEmpty(){return!1}canIndent(){return!1}append(...a){for(let c=0;c<a.length;c++){var b=a[c];if(p(b))super.append(b);else{let d=y();q(b)?d.append(b):h.$isElementNode(b)?(b=h.$createTextNode(b.getTextContent()),d.append(b)):d.append(b);super.append(d)}}F(this);return this}extractWithChild(a){return p(a)}}
function P(a,b,c){let d=[],e=[];var f=b.list;if(void 0!==f){let m=f[`${c.__tag}Depth`]||[];b=n(c)-1;let x=b%m.length;var g=m[x];let N=f[c.__tag],A,B=f.nested;f=f.checklist;void 0!==B&&B.list&&(A=B.list);void 0!==N&&d.push(N);void 0!==f&&"check"===c.__listType&&d.push(f);if(void 0!==g)for(g=g.split(" "),d.push(...g),g=0;g<m.length;g++)g!==x&&e.push(c.__tag+g);void 0!==A&&(c=A.split(" "),1<b?d.push(...c):e.push(...c))}0<e.length&&k.removeClassNamesFromElement(a,...e);0<d.length&&k.addClassNamesToElement(a,
...d)}function R(a){let b=[];for(let d=0;d<a.length;d++){var c=a[d];p(c)?(b.push(c),c=c.getChildren(),1<c.length&&c.forEach(e=>{q(e)&&b.push(w(e))})):b.push(w(c))}return b}function Q(a){let b=a.nodeName.toLowerCase(),c=null;"ol"===b?c=E("number",a.start):"ul"===b&&(c=k.isHTMLElement(a)&&"check"===a.getAttribute("__lexicallisttype")?E("check"):E("bullet"));return{after:R,node:c}}let O={ol:"number",ul:"bullet"};function E(a,b=1){return h.$applyNodeReplacement(new M(a,b))}
function q(a){return a instanceof M}let S=h.createCommand("INSERT_UNORDERED_LIST_COMMAND"),T=h.createCommand("INSERT_ORDERED_LIST_COMMAND"),U=h.createCommand("INSERT_CHECK_LIST_COMMAND"),V=h.createCommand("REMOVE_LIST_COMMAND");exports.$createListItemNode=y;exports.$createListNode=E;exports.$getListDepth=n;
exports.$handleListInsertParagraph=function(){var a=h.$getSelection();if(!h.$isRangeSelection(a)||!a.isCollapsed())return!1;a=a.anchor.getNode();if(!p(a)||0!==a.getChildrenSize())return!1;var b=r(a),c=a.getParent();q(c)||l(40);let d=c.getParent(),e;if(h.$isRootOrShadowRoot(d))e=h.$createParagraphNode(),b.insertAfter(e);else if(p(d))e=y(),d.insertAfter(e);else return!1;e.select();b=a.getNextSiblings();if(0<b.length){let f=E(c.getListType());h.$isParagraphNode(e)?e.insertAfter(f):(c=y(),c.append(f),
e.insertAfter(c));b.forEach(g=>{g.remove();f.append(g)})}v(a);return!0};exports.$isListItemNode=p;exports.$isListNode=q;exports.INSERT_CHECK_LIST_COMMAND=U;exports.INSERT_ORDERED_LIST_COMMAND=T;exports.INSERT_UNORDERED_LIST_COMMAND=S;exports.ListItemNode=I;exports.ListNode=M;exports.REMOVE_LIST_COMMAND=V;
exports.insertList=function(a,b){a.update(()=>{var c=h.$getSelection();if(null!==c){var d=c.getNodes();c=c.getStartEndPoints();null===c&&l(143);[c]=c;c=c.getNode();var e=c.getParent();if(z(c,d))d=E(b),h.$isRootOrShadowRoot(e)?(c.replace(d),e=y(),h.$isElementNode(c)&&(e.setFormat(c.getFormatType()),e.setIndent(c.getIndent())),d.append(e)):p(c)&&(c=c.getParentOrThrow(),C(d,c.getChildren()),c.replace(d));else for(c=new Set,e=0;e<d.length;e++){var f=d[e];if(h.$isElementNode(f)&&f.isEmpty()&&!p(f)&&!c.has(f.getKey()))D(f,
b);else if(h.$isLeafNode(f))for(f=f.getParent();null!=f;){let m=f.getKey();if(q(f)){if(!c.has(m)){var g=E(b);C(g,f.getChildren());f.replace(g);F(g);c.add(m)}break}else{g=f.getParent();if(h.$isRootOrShadowRoot(g)&&!c.has(m)){c.add(m);D(f,b);break}f=g}}}}})};
exports.removeList=function(a){a.update(()=>{let b=h.$getSelection();if(h.$isRangeSelection(b)){var c=new Set,d=b.getNodes(),e=b.anchor.getNode();if(z(e,d))c.add(r(e));else for(e=0;e<d.length;e++){var f=d[e];h.$isLeafNode(f)&&(f=k.$getNearestNodeOfType(f,I),null!=f&&c.add(r(f)))}for(let g of c){c=g;d=t(g);for(let m of d)d=h.$createParagraphNode(),C(d,m.getChildren()),c.insertAfter(d),c=d,m.__key===b.anchor.key&&b.anchor.set(d.getKey(),0,"element"),m.__key===b.focus.key&&b.focus.set(d.getKey(),0,"element"),
m.remove();g.remove()}}})}

/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var h=require("@lexical/utils"),v=require("lexical");let ca=/^(\d+(?:\.\d+)?)px$/,w={BOTH:3,COLUMN:2,NO_STATUS:0,ROW:1};
class y extends v.ElementNode{static getType(){return"tablecell"}static clone(a){let b=new y(a.__headerState,a.__colSpan,a.__width,a.__key);b.__rowSpan=a.__rowSpan;b.__backgroundColor=a.__backgroundColor;return b}static importDOM(){return{td:()=>({conversion:da,priority:0}),th:()=>({conversion:da,priority:0})}}static importJSON(a){let b=a.rowSpan||1,c=z(a.headerState,a.colSpan||1,a.width||void 0);c.__rowSpan=b;c.__backgroundColor=a.backgroundColor||null;return c}constructor(a=w.NO_STATUS,b=1,c,d){super(d);
this.__colSpan=b;this.__rowSpan=1;this.__headerState=a;this.__width=c;this.__backgroundColor=null}createDOM(a){let b=document.createElement(this.getTag());this.__width&&(b.style.width=`${this.__width}px`);1<this.__colSpan&&(b.colSpan=this.__colSpan);1<this.__rowSpan&&(b.rowSpan=this.__rowSpan);null!==this.__backgroundColor&&(b.style.backgroundColor=this.__backgroundColor);h.addClassNamesToElement(b,a.theme.tableCell,this.hasHeader()&&a.theme.tableCellHeader);return b}exportDOM(a){({element:a}=super.exportDOM(a));
if(a){var b=this.getParentOrThrow().getChildrenSize();a.style.border="1px solid black";1<this.__colSpan&&(a.colSpan=this.__colSpan);1<this.__rowSpan&&(a.rowSpan=this.__rowSpan);a.style.width=`${this.getWidth()||Math.max(90,700/b)}px`;a.style.verticalAlign="top";a.style.textAlign="start";b=this.getBackgroundColor();null!==b?a.style.backgroundColor=b:this.hasHeader()&&(a.style.backgroundColor="#f2f3f5")}return{element:a}}exportJSON(){return{...super.exportJSON(),backgroundColor:this.getBackgroundColor(),
colSpan:this.__colSpan,headerState:this.__headerState,rowSpan:this.__rowSpan,type:"tablecell",width:this.getWidth()}}getColSpan(){return this.__colSpan}setColSpan(a){this.getWritable().__colSpan=a;return this}getRowSpan(){return this.__rowSpan}setRowSpan(a){this.getWritable().__rowSpan=a;return this}getTag(){return this.hasHeader()?"th":"td"}setHeaderStyles(a){this.getWritable().__headerState=a;return this.__headerState}getHeaderStyles(){return this.getLatest().__headerState}setWidth(a){this.getWritable().__width=
a;return this.__width}getWidth(){return this.getLatest().__width}getBackgroundColor(){return this.getLatest().__backgroundColor}setBackgroundColor(a){this.getWritable().__backgroundColor=a}toggleHeaderStyle(a){let b=this.getWritable();b.__headerState=(b.__headerState&a)===a?b.__headerState-a:b.__headerState+a;return b}hasHeaderState(a){return(this.getHeaderStyles()&a)===a}hasHeader(){return this.getLatest().__headerState!==w.NO_STATUS}updateDOM(a){return a.__headerState!==this.__headerState||a.__width!==
this.__width||a.__colSpan!==this.__colSpan||a.__rowSpan!==this.__rowSpan||a.__backgroundColor!==this.__backgroundColor}isShadowRoot(){return!0}collapseAtStart(){return!0}canBeEmpty(){return!1}canIndent(){return!1}}
function da(a){var b=a.nodeName.toLowerCase();let c=void 0;ca.test(a.style.width)&&(c=parseFloat(a.style.width));b=z("th"===b?w.ROW:w.NO_STATUS,a.colSpan,c);b.__rowSpan=a.rowSpan;a=a.style.backgroundColor;""!==a&&(b.__backgroundColor=a);return{forChild:(d,e)=>{if(A(e)&&!v.$isElementNode(d)){e=v.$createParagraphNode();if(v.$isLineBreakNode(d)&&"\n"===d.getTextContent())return null;e.append(d);return e}return d},node:b}}function z(a,b=1,c){return v.$applyNodeReplacement(new y(a,b,c))}
function A(a){return a instanceof y}let ea=v.createCommand("INSERT_TABLE_COMMAND");
class B extends v.ElementNode{static getType(){return"tablerow"}static clone(a){return new B(a.__height,a.__key)}static importDOM(){return{tr:()=>({conversion:fa,priority:0})}}static importJSON(a){return D(a.height)}constructor(a,b){super(b);this.__height=a}exportJSON(){return{...super.exportJSON(),type:"tablerow",version:1}}createDOM(a){let b=document.createElement("tr");this.__height&&(b.style.height=`${this.__height}px`);h.addClassNamesToElement(b,a.theme.tableRow);return b}isShadowRoot(){return!0}setHeight(a){this.getWritable().__height=
a;return this.__height}getHeight(){return this.getLatest().__height}updateDOM(a){return a.__height!==this.__height}canBeEmpty(){return!1}canIndent(){return!1}}function fa(a){let b=void 0;ca.test(a.style.height)&&(b=parseFloat(a.style.height));return{node:D(b)}}function D(a){return v.$applyNodeReplacement(new B(a))}function F(a){return a instanceof B}
function H(a){let b=new URLSearchParams;b.append("code",a);for(let c=1;c<arguments.length;c++)b.append("v",arguments[c]);throw Error(`Minified Lexical error #${a}; visit https://lexical.dev/docs/error?${b} for the full message or `+"use the non-minified dev environment for full errors and additional helpful warnings.");}let ha="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;
function ia(a){a=h.$findMatchingParent(a,b=>F(b));if(F(a))return a;throw Error("Expected table cell to be inside of table row.");}function ja(a){a=h.$findMatchingParent(a,b=>I(b));if(I(a))return a;throw Error("Expected table cell to be inside of table.");}function ka(a,b){let c=ja(a),{x:d,y:e}=c.getCordsFromCellNode(a,b);return{above:c.getCellNodeFromCords(d,e-1,b),below:c.getCellNodeFromCords(d,e+1,b),left:c.getCellNodeFromCords(d-1,e,b),right:c.getCellNodeFromCords(d+1,e,b)}}
let la=(a,b)=>a===w.BOTH||a===b?b:w.NO_STATUS;function J(a){let b=a.getFirstDescendant();null==b?a.selectStart():b.getParentOrThrow().selectStart()}function ma(a,b){let c=a.getFirstChild();null!==c?c.insertBefore(b):a.append(b)}
function L(a,b,c){let d=[],e=null,k=null;a=a.getChildren();for(let g=0;g<a.length;g++){var f=a[g];F(f)||H(146);var m=f.getChildren();f=0;for(let l of m){for(A(l)||H(147);void 0!==d[g]&&void 0!==d[g][f];)f++;m=g;var q=f,r=l;let n={cell:r,startColumn:q,startRow:m},p=r.__rowSpan,t=r.__colSpan;for(let u=0;u<p;u++){void 0===d[m+u]&&(d[m+u]=[]);for(let x=0;x<t;x++)d[m+u][q+x]=n}b.is(r)&&(e=n);c.is(r)&&(k=n);f+=l.__colSpan}}null===e&&H(110);null===k&&H(111);return[d,e,k]}
function M(a){a instanceof y||("__type"in a?(a=h.$findMatchingParent(a,A),A(a)||H(148)):(a=h.$findMatchingParent(a.getNode(),A),A(a)||H(148)));let b=a.getParent();F(b)||H(149);let c=b.getParent();I(c)||H(150);return[a,b,c]}
function oa(a){let [b,,c]=M(a);a=c.getChildren();let d=a.length;var e=a[0].getChildren().length;let k=Array(d);for(var f=0;f<d;f++)k[f]=Array(e);for(e=0;e<d;e++){f=a[e].getChildren();let m=0;for(let q=0;q<f.length;q++){for(;k[e][m];)m++;let r=f[q],g=r.__rowSpan||1,l=r.__colSpan||1;for(let n=0;n<g;n++)for(let p=0;p<l;p++)k[e+n][m+p]=r;if(b===r)return{colSpan:l,columnIndex:m,rowIndex:e,rowSpan:g};m+=l}}return null}
class pa{constructor(a,b,c){this.anchor=b;this.focus=c;b._selection=this;c._selection=this;this._cachedNodes=null;this.dirty=!1;this.tableKey=a}getStartEndPoints(){return[this.anchor,this.focus]}isBackward(){return this.focus.isBefore(this.anchor)}getCachedNodes(){return this._cachedNodes}setCachedNodes(a){this._cachedNodes=a}is(a){return N(a)?this.tableKey===a.tableKey&&this.anchor.is(a.anchor)&&this.focus.is(a.focus):!1}set(a,b,c){this.dirty=!0;this.tableKey=a;this.anchor.key=b;this.focus.key=c;
this._cachedNodes=null}clone(){return new pa(this.tableKey,this.anchor,this.focus)}isCollapsed(){return!1}extract(){return this.getNodes()}insertRawText(){}insertText(){}insertNodes(a){let b=this.focus.getNode();v.$isElementNode(b)||H(151);v.$normalizeSelection__EXPERIMENTAL(b.select(0,b.getChildrenSize())).insertNodes(a)}getShape(){var a=v.$getNodeByKey(this.anchor.key);A(a)||H(152);a=oa(a);null===a&&H(153);var b=v.$getNodeByKey(this.focus.key);A(b)||H(154);let c=oa(b);null===c&&H(155);b=Math.min(a.columnIndex,
c.columnIndex);let d=Math.max(a.columnIndex,c.columnIndex),e=Math.min(a.rowIndex,c.rowIndex);a=Math.max(a.rowIndex,c.rowIndex);return{fromX:Math.min(b,d),fromY:Math.min(e,a),toX:Math.max(b,d),toY:Math.max(e,a)}}getNodes(){function a(u){let {cell:x,startColumn:E,startRow:C}=u;q=Math.min(q,E);r=Math.min(r,C);g=Math.max(g,E+x.__colSpan-1);l=Math.max(l,C+x.__rowSpan-1)}var b=this._cachedNodes;if(null!==b)return b;var c=this.anchor.getNode();b=this.focus.getNode();var d=h.$findMatchingParent(c,A);c=h.$findMatchingParent(b,
A);A(d)||H(152);A(c)||H(154);b=d.getParent();F(b)||H(156);b=b.getParent();I(b)||H(157);var e=c.getParents()[1];if(e!==b)return b.isParentOf(c)?(b=e.getParent(),null==b&&H(159),this.set(this.tableKey,c.getKey(),b.getKey())):(b=b.getParent(),null==b&&H(158),this.set(this.tableKey,b.getKey(),c.getKey())),this.getNodes();let [k,f,m]=L(b,d,c),q=Math.min(f.startColumn,m.startColumn),r=Math.min(f.startRow,m.startRow),g=Math.max(f.startColumn+f.cell.__colSpan-1,m.startColumn+m.cell.__colSpan-1),l=Math.max(f.startRow+
f.cell.__rowSpan-1,m.startRow+m.cell.__rowSpan-1);c=q;d=r;e=q;for(var n=r;q<c||r<d||g>e||l>n;){if(q<c){var p=n-d;--c;for(var t=0;t<=p;t++)a(k[d+t][c])}if(r<d)for(p=e-c,--d,t=0;t<=p;t++)a(k[d][c+t]);if(g>e)for(p=n-d,e+=1,t=0;t<=p;t++)a(k[d+t][e]);if(l>n)for(p=e-c,n+=1,t=0;t<=p;t++)a(k[n][c+t])}b=[b];c=null;for(d=r;d<=l;d++)for(e=q;e<=g;e++)({cell:n}=k[d][e]),p=n.getParent(),F(p)||H(160),p!==c&&b.push(p),b.push(n,...qa(n)),c=p;v.isCurrentlyReadOnlyMode()||(this._cachedNodes=b);return b}getTextContent(){let a=
this.getNodes(),b="";for(let c=0;c<a.length;c++)b+=a[c].getTextContent();return b}}function N(a){return a instanceof pa}function O(){let a=v.$createPoint("root",0,"element"),b=v.$createPoint("root",0,"element");return new pa("root",a,b)}function qa(a){let b=[],c=[a];for(;0<c.length;){let d=c.pop();void 0===d&&H(112);v.$isElementNode(d)&&c.unshift(...d.getChildren());d!==a&&b.push(d)}return b}
class ra{constructor(a,b){this.isHighlightingCells=!1;this.focusY=this.focusX=this.anchorY=this.anchorX=-1;this.listenersToRemove=new Set;this.tableNodeKey=b;this.editor=a;this.table={columns:0,domRows:[],rows:0};this.focusCell=this.anchorCell=this.focusCellNodeKey=this.anchorCellNodeKey=this.tableSelection=null;this.hasHijackedSelectionStyles=!1;this.trackTable()}getTable(){return this.table}removeListeners(){Array.from(this.listenersToRemove).forEach(a=>a())}trackTable(){let a=new MutationObserver(b=>
{this.editor.update(()=>{var c=!1;for(let d=0;d<b.length;d++){const e=b[d].target.nodeName;if("TABLE"===e||"TR"===e){c=!0;break}}if(c){c=this.editor.getElementByKey(this.tableNodeKey);if(!c)throw Error("Expected to find TableElement in DOM");this.table=P(c)}})});this.editor.update(()=>{let b=this.editor.getElementByKey(this.tableNodeKey);if(!b)throw Error("Expected to find TableElement in DOM");this.table=P(b);a.observe(b,{childList:!0,subtree:!0})})}clearHighlight(){let a=this.editor;this.isHighlightingCells=
!1;this.focusY=this.focusX=this.anchorY=this.anchorX=-1;this.focusCell=this.anchorCell=this.focusCellNodeKey=this.anchorCellNodeKey=this.tableSelection=null;this.hasHijackedSelectionStyles=!1;this.enableHighlightStyle();a.update(()=>{var b=v.$getNodeByKey(this.tableNodeKey);if(!I(b))throw Error("Expected TableNode.");b=a.getElementByKey(this.tableNodeKey);if(!b)throw Error("Expected to find TableElement in DOM");b=P(b);Q(a,b,null);v.$setSelection(null);a.dispatchCommand(v.SELECTION_CHANGE_COMMAND,
void 0)})}enableHighlightStyle(){let a=this.editor;a.update(()=>{let b=a.getElementByKey(this.tableNodeKey);if(!b)throw Error("Expected to find TableElement in DOM");h.removeClassNamesFromElement(b,a._config.theme.tableSelection);b.classList.remove("disable-selection");this.hasHijackedSelectionStyles=!1})}disableHighlightStyle(){let a=this.editor;a.update(()=>{let b=a.getElementByKey(this.tableNodeKey);if(!b)throw Error("Expected to find TableElement in DOM");h.addClassNamesToElement(b,a._config.theme.tableSelection);
this.hasHijackedSelectionStyles=!0})}updateTableTableSelection(a){if(null!==a&&a.tableKey===this.tableNodeKey){let b=this.editor;this.tableSelection=a;this.isHighlightingCells=!0;this.disableHighlightStyle();Q(b,this.table,this.tableSelection)}else null==a?this.clearHighlight():(this.tableNodeKey=a.tableKey,this.updateTableTableSelection(a))}setFocusCellForSelection(a,b=!1){let c=this.editor;c.update(()=>{var d=v.$getNodeByKey(this.tableNodeKey);if(!I(d))throw Error("Expected TableNode.");if(!c.getElementByKey(this.tableNodeKey))throw Error("Expected to find TableElement in DOM");
d=a.x;let e=a.y;this.focusCell=a;if(null!==this.anchorCell){let k=ha?(c._window||window).getSelection():null;k&&k.setBaseAndExtent(this.anchorCell.elem,0,this.focusCell.elem,0)}if(!this.isHighlightingCells&&(this.anchorX!==d||this.anchorY!==e||b))this.isHighlightingCells=!0,this.disableHighlightStyle();else if(d===this.focusX&&e===this.focusY)return;this.focusX=d;this.focusY=e;this.isHighlightingCells&&(d=v.$getNearestNodeFromDOMNode(a.elem),null!=this.tableSelection&&null!=this.anchorCellNodeKey&&
A(d)&&(d=d.getKey(),this.tableSelection=this.tableSelection.clone()||O(),this.focusCellNodeKey=d,this.tableSelection.set(this.tableNodeKey,this.anchorCellNodeKey,this.focusCellNodeKey),v.$setSelection(this.tableSelection),c.dispatchCommand(v.SELECTION_CHANGE_COMMAND,void 0),Q(c,this.table,this.tableSelection)))})}setAnchorCellForSelection(a){this.isHighlightingCells=!1;this.anchorCell=a;this.anchorX=a.x;this.anchorY=a.y;this.editor.update(()=>{var b=v.$getNearestNodeFromDOMNode(a.elem);A(b)&&(b=b.getKey(),
this.tableSelection=null!=this.tableSelection?this.tableSelection.clone():O(),this.anchorCellNodeKey=b)})}formatCells(a){this.editor.update(()=>{let b=v.$getSelection();N(b)||H(11);let c=v.$createRangeSelection(),d=c.anchor,e=c.focus;b.getNodes().forEach(k=>{A(k)&&0!==k.getTextContentSize()&&(d.set(k.getKey(),0,"element"),e.set(k.getKey(),k.getChildrenSize(),"element"),c.formatText(a))});v.$setSelection(b);this.editor.dispatchCommand(v.SELECTION_CHANGE_COMMAND,void 0)})}clearText(){let a=this.editor;
a.update(()=>{let b=v.$getNodeByKey(this.tableNodeKey);if(!I(b))throw Error("Expected TableNode.");var c=v.$getSelection();N(c)||H(11);c=c.getNodes().filter(A);c.length===this.table.columns*this.table.rows?(b.selectPrevious(),b.remove(),v.$getRoot().selectStart()):(c.forEach(d=>{if(v.$isElementNode(d)){let e=v.$createParagraphNode(),k=v.$createTextNode();e.append(k);d.append(e);d.getChildren().forEach(f=>{f!==e&&f.remove()})}}),Q(a,this.table,null),v.$setSelection(null),a.dispatchCommand(v.SELECTION_CHANGE_COMMAND,
void 0))})}}function sa(a){for(;null!=a;){let b=a.nodeName;if("TD"===b||"TH"===b){a=a._cell;if(void 0===a)break;return a}a=a.parentNode}return null}
function P(a){let b=[],c={columns:0,domRows:b,rows:0};var d=a.firstChild;let e=a=0;for(b.length=0;null!=d;){var k=d.nodeName;if("TD"===k||"TH"===k){k=d;k={elem:k,hasBackgroundColor:""!==k.style.backgroundColor,highlighted:!1,x:a,y:e};d._cell=k;let f=b[e];void 0===f&&(f=b[e]=[]);f[a]=k}else if(k=d.firstChild,null!=k){d=k;continue}k=d.nextSibling;if(null!=k)a++,d=k;else if(k=d.parentNode,null!=k){d=k.nextSibling;if(null==d)break;e++;a=0}}c.columns=a+1;c.rows=e+1;return c}
function Q(a,b,c){let d=new Set(c?c.getNodes():[]);ta(b,(e,k)=>{let f=e.elem;d.has(k)?(e.highlighted=!0,ua(a,e)):(e.highlighted=!1,va(a,e),f.getAttribute("style")||f.removeAttribute("style"))})}function ta(a,b){({domRows:a}=a);for(let c=0;c<a.length;c++){let d=a[c];if(d)for(let e=0;e<d.length;e++){let k=d[e];if(!k)continue;let f=v.$getNearestNodeFromDOMNode(k.elem);null!==f&&b(k,f,{x:e,y:c})}}}function xa(a,b){b.disableHighlightStyle();ta(b.table,c=>{c.highlighted=!0;ua(a,c)})}
function ya(a,b){b.enableHighlightStyle();ta(b.table,c=>{let d=c.elem;c.highlighted=!1;va(a,c);d.getAttribute("style")||d.removeAttribute("style")})}
let za=(a,b,c,d,e)=>{const k="forward"===e;switch(e){case "backward":case "forward":return c!==(k?a.table.columns-1:0)?(a=b.getCellNodeFromCordsOrThrow(c+(k?1:-1),d,a.table),k?a.selectStart():a.selectEnd()):d!==(k?a.table.rows-1:0)?(a=b.getCellNodeFromCordsOrThrow(k?0:a.table.columns-1,d+(k?1:-1),a.table),k?a.selectStart():a.selectEnd()):k?b.selectNext():b.selectPrevious(),!0;case "up":return 0!==d?b.getCellNodeFromCordsOrThrow(c,d-1,a.table).selectEnd():b.selectPrevious(),!0;case "down":return d!==
a.table.rows-1?b.getCellNodeFromCordsOrThrow(c,d+1,a.table).selectStart():b.selectNext(),!0;default:return!1}},Aa=(a,b,c,d,e)=>{const k="forward"===e;switch(e){case "backward":case "forward":return c!==(k?a.table.columns-1:0)&&a.setFocusCellForSelection(b.getDOMCellFromCordsOrThrow(c+(k?1:-1),d,a.table)),!0;case "up":return 0!==d?(a.setFocusCellForSelection(b.getDOMCellFromCordsOrThrow(c,d-1,a.table)),!0):!1;case "down":return d!==a.table.rows-1?(a.setFocusCellForSelection(b.getDOMCellFromCordsOrThrow(c,
d+1,a.table)),!0):!1;default:return!1}};function V(a,b){if(v.$isRangeSelection(a)||N(a)){let c=b.isParentOf(a.anchor.getNode());a=b.isParentOf(a.focus.getNode());return c&&a}return!1}
function ua(a,b){a=b.elem;b=v.$getNearestNodeFromDOMNode(a);A(b)||H(131);null===b.getBackgroundColor()?a.style.setProperty("background-color","rgb(172,206,247)"):a.style.setProperty("background-image","linear-gradient(to right, rgba(172,206,247,0.85), rgba(172,206,247,0.85))");a.style.setProperty("caret-color","transparent")}
function va(a,b){a=b.elem;b=v.$getNearestNodeFromDOMNode(a);A(b)||H(131);null===b.getBackgroundColor()&&a.style.removeProperty("background-color");a.style.removeProperty("background-image");a.style.removeProperty("caret-color")}function Ba(a){a=h.$findMatchingParent(a,A);return A(a)?a:null}function Ca(a){a=h.$findMatchingParent(a,I);return I(a)?a:null}
function W(a,b,c,d,e){let k=v.$getSelection();if(!V(k,d))return!1;if(v.$isRangeSelection(k)&&k.isCollapsed()){if("backward"===c||"forward"===c)return!1;let {anchor:r,focus:g}=k;var f=h.$findMatchingParent(r.getNode(),A),m=h.$findMatchingParent(g.getNode(),A);if(!A(f)||!f.is(m))return!1;m=Ca(f);if(m!==d&&null!=m){var q=a.getElementByKey(m.getKey());if(null!=q)return e.table=P(q),W(a,b,c,m,e)}m=a.getElementByKey(f.__key);q=a.getElementByKey(r.key);if(null==q||null==m)return!1;if("element"===r.type)m=
q.getBoundingClientRect();else{m=window.getSelection();if(null===m||0===m.rangeCount)return!1;m=m.getRangeAt(0).getBoundingClientRect()}q="up"===c?f.getFirstChild():f.getLastChild();if(null==q)return!1;a=a.getElementByKey(q.__key);if(null==a)return!1;a=a.getBoundingClientRect();if("up"===c?a.top>m.top-m.height:m.bottom+m.height>a.bottom){X(b);a=d.getCordsFromCellNode(f,e.table);if(b.shiftKey)c=d.getDOMCellFromCordsOrThrow(a.x,a.y,e.table),e.setAnchorCellForSelection(c),e.setFocusCellForSelection(c,
!0);else return za(e,d,a.x,a.y,c);return!0}}else if(N(k)){let {anchor:r,focus:g}=k;q=h.$findMatchingParent(r.getNode(),A);m=h.$findMatchingParent(g.getNode(),A);[f]=k.getNodes();a=a.getElementByKey(f.getKey());if(!A(q)||!A(m)||!I(f)||null==a)return!1;e.updateTableTableSelection(k);a=P(a);q=d.getCordsFromCellNode(q,a);q=d.getDOMCellFromCordsOrThrow(q.x,q.y,a);e.setAnchorCellForSelection(q);X(b);if(b.shiftKey)return b=d.getCordsFromCellNode(m,a),Aa(e,f,b.x,b.y,c);m.selectEnd();return!0}return!1}
function X(a){a.preventDefault();a.stopImmediatePropagation();a.stopPropagation()}
class Y extends v.ElementNode{static getType(){return"table"}static clone(a){return new Y(a.__key)}static importDOM(){return{table:()=>({conversion:Da,priority:1})}}static importJSON(){return Z()}constructor(a){super(a)}exportJSON(){return{...super.exportJSON(),type:"table",version:1}}createDOM(a){let b=document.createElement("table");h.addClassNamesToElement(b,a.theme.table);return b}updateDOM(){return!1}exportDOM(a){return{...super.exportDOM(a),after:b=>{if(b){let c=b.cloneNode(),d=document.createElement("colgroup"),
e=document.createElement("tbody");h.isHTMLElement(b)&&e.append(...b.children);b=this.getFirstChildOrThrow();if(!F(b))throw Error("Expected to find row node.");b=b.getChildrenSize();for(let k=0;k<b;k++){let f=document.createElement("col");d.append(f)}c.replaceChildren(d,e);return c}}}}canExtractContents(){return!1}canBeEmpty(){return!1}isShadowRoot(){return!0}getCordsFromCellNode(a,b){let {rows:c,domRows:d}=b;for(b=0;b<c;b++){var e=d[b];if(null!=e&&(e=e.findIndex(k=>{if(k)return{elem:k}=k,v.$getNearestNodeFromDOMNode(k)===
a}),-1!==e))return{x:e,y:b}}throw Error("Cell not found in table.");}getDOMCellFromCords(a,b,c){({domRows:c}=c);b=c[b];if(null==b)return null;a=b[a];return null==a?null:a}getDOMCellFromCordsOrThrow(a,b,c){a=this.getDOMCellFromCords(a,b,c);if(!a)throw Error("Cell not found at cords.");return a}getCellNodeFromCords(a,b,c){a=this.getDOMCellFromCords(a,b,c);if(null==a)return null;a=v.$getNearestNodeFromDOMNode(a.elem);return A(a)?a:null}getCellNodeFromCordsOrThrow(a,b,c){a=this.getCellNodeFromCords(a,
b,c);if(!a)throw Error("Node at cords not TableCellNode.");return a}canSelectBefore(){return!0}canIndent(){return!1}}function Da(){return{node:Z()}}function Z(){return v.$applyNodeReplacement(new Y)}function I(a){return a instanceof Y}exports.$computeTableMap=L;exports.$createTableCellNode=z;exports.$createTableNode=Z;
exports.$createTableNodeWithDimensions=function(a,b,c=!0){let d=Z();for(let k=0;k<a;k++){let f=D();for(let m=0;m<b;m++){var e=w.NO_STATUS;"object"===typeof c?(0===k&&c.rows&&(e|=w.ROW),0===m&&c.columns&&(e|=w.COLUMN)):c&&(0===k&&(e|=w.ROW),0===m&&(e|=w.COLUMN));e=z(e);let q=v.$createParagraphNode();q.append(v.$createTextNode());e.append(q);f.append(e)}d.append(f)}return d};exports.$createTableRowNode=D;exports.$createTableSelection=O;
exports.$deleteTableColumn=function(a,b){let c=a.getChildren();for(let e=0;e<c.length;e++){var d=c[e];if(F(d)){d=d.getChildren();if(b>=d.length||0>b)throw Error("Table column target index out of range");d[b].remove()}}return a};
exports.$deleteTableColumn__EXPERIMENTAL=function(){var a=v.$getSelection();v.$isRangeSelection(a)||N(a)||H(118);var b=a.anchor.getNode();a=a.focus.getNode();let [c,,d]=M(b);[b]=M(a);let [e,k,f]=L(d,c,b);var {startColumn:m}=k;let {startRow:q,startColumn:r}=f;a=Math.min(m,r);m=Math.max(m+c.__colSpan-1,r+b.__colSpan-1);let g=m-a+1;if(e[0].length===m-a+1)d.selectPrevious(),d.remove();else{var l=e.length;for(let n=0;n<l;n++)for(let p=a;p<=m;p++){let {cell:t,startColumn:u}=e[n][p];u<a?p===a&&t.setColSpan(t.__colSpan-
Math.min(g,t.__colSpan-(a-u))):u+t.__colSpan-1>m?p===m&&t.setColSpan(t.__colSpan-(m-u+1)):t.remove()}a=e[q];b=a[r+b.__colSpan];void 0!==b?({cell:b}=b,J(b)):({cell:b}=a[r-1],J(b))}};
exports.$deleteTableRow__EXPERIMENTAL=function(){var a=v.$getSelection();v.$isRangeSelection(a)||N(a)||H(118);var b=a.anchor.getNode();a=a.focus.getNode();let [c,,d]=M(b);[a]=M(a);let [e,k,f]=L(d,c,a);({startRow:b}=k);var {startRow:m}=f;a=m+a.__rowSpan-1;if(e.length===a-b+1)d.remove();else{m=e[0].length;var q=e[a+1],r=d.getChildAtIndex(a+1);for(let l=a;l>=b;l--){for(var g=m-1;0<=g;g--){let {cell:n,startRow:p,startColumn:t}=e[l][g];if(t===g&&(l===b&&p<b&&n.setRowSpan(n.__rowSpan-(p-b)),p>=b&&p+n.__rowSpan-
1>a))if(n.setRowSpan(n.__rowSpan-(a-p+1)),null===r&&H(122),0===g)ma(r,n);else{let {cell:u}=q[g-1];u.insertAfter(n)}}g=d.getChildAtIndex(l);F(g)||H(123,String(l));g.remove()}void 0!==q?({cell:b}=q[0],J(b)):({cell:b}=e[b-1][0],J(b))}};exports.$getElementForTableNode=function(a,b){a=a.getElementByKey(b.getKey());if(null==a)throw Error("Table Element Not Found");return P(a)};exports.$getNodeTriplet=M;
exports.$getTableCellNodeFromLexicalNode=function(a){a=h.$findMatchingParent(a,b=>A(b));return A(a)?a:null};exports.$getTableCellNodeRect=oa;exports.$getTableColumnIndexFromTableCellNode=function(a){return ia(a).getChildren().findIndex(b=>b.is(a))};exports.$getTableNodeFromLexicalNodeOrThrow=ja;exports.$getTableRowIndexFromTableCellNode=function(a){let b=ia(a);return ja(b).getChildren().findIndex(c=>c.is(b))};exports.$getTableRowNodeFromTableCellNodeOrThrow=ia;
exports.$insertTableColumn=function(a,b,c=!0,d,e){let k=a.getChildren(),f=[];for(let r=0;r<k.length;r++){let g=k[r];if(F(g))for(let l=0;l<d;l++){var m=g.getChildren();if(b>=m.length||0>b)throw Error("Table column target index out of range");m=m[b];A(m)||H(12);let {left:n,right:p}=ka(m,e);var q=w.NO_STATUS;if(n&&n.hasHeaderState(w.ROW)||p&&p.hasHeaderState(w.ROW))q|=w.ROW;q=z(q);q.append(v.$createParagraphNode());f.push({newTableCell:q,targetCell:m})}}f.forEach(({newTableCell:r,targetCell:g})=>{c?
g.insertAfter(r):g.insertBefore(r)});return a};
exports.$insertTableColumn__EXPERIMENTAL=function(a=!0){function b(l=w.NO_STATUS){l=z(l).append(v.$createParagraphNode());null===r&&(r=l);return l}var c=v.$getSelection();v.$isRangeSelection(c)||N(c)||H(118);var d=c.anchor.getNode();c=c.focus.getNode();[d]=M(d);let [e,,k]=M(c),[f,m,q]=L(k,e,d);d=f.length;c=a?Math.max(m.startColumn,q.startColumn):Math.min(m.startColumn,q.startColumn);a=a?c+e.__colSpan-1:c-1;c=k.getFirstChild();F(c)||H(120);let r=null;var g=c;a:for(c=0;c<d;c++){0!==c&&(g=g.getNextSibling(),
F(g)||H(121));let l=f[c],n=la(l[0>a?0:a].cell.__headerState,w.ROW);if(0>a){ma(g,b(n));continue}let {cell:p,startColumn:t,startRow:u}=l[a];if(t+p.__colSpan-1<=a){let x=p,E=u,C=a;for(;E!==c&&1<x.__rowSpan;)if(C-=p.__colSpan,0<=C){let {cell:K,startRow:R}=l[C];x=K;E=R}else{g.append(b(n));continue a}x.insertAfter(b(n))}else p.setColSpan(p.__colSpan+1)}null!==r&&J(r)};
exports.$insertTableRow=function(a,b,c=!0,d,e){var k=a.getChildren();if(b>=k.length||0>b)throw Error("Table row target index out of range");b=k[b];if(F(b))for(k=0;k<d;k++){let m=b.getChildren(),q=m.length,r=D();for(let g=0;g<q;g++){var f=m[g];A(f)||H(12);let {above:l,below:n}=ka(f,e);f=w.NO_STATUS;let p=l&&l.getWidth()||n&&n.getWidth()||void 0;if(l&&l.hasHeaderState(w.COLUMN)||n&&n.hasHeaderState(w.COLUMN))f|=w.COLUMN;f=z(f,1,p);f.append(v.$createParagraphNode());r.append(f)}c?b.insertAfter(r):b.insertBefore(r)}else throw Error("Row before insertion index does not exist.");
return a};
exports.$insertTableRow__EXPERIMENTAL=function(a=!0){var b=v.$getSelection();v.$isRangeSelection(b)||N(b)||H(118);b=b.focus.getNode();let [c,,d]=M(b),[e,k]=L(d,c,c);b=e[0].length;var {startRow:f}=k;if(a){a=f+c.__rowSpan-1;var m=e[a];f=D();for(var q=0;q<b;q++){let {cell:g,startRow:l}=m[q];if(l+g.__rowSpan-1<=a){var r=la(m[q].cell.__headerState,w.COLUMN);f.append(z(r).append(v.$createParagraphNode()))}else g.setRowSpan(g.__rowSpan+1)}b=d.getChildAtIndex(a);F(b)||H(145);b.insertAfter(f)}else{m=e[f];
a=D();for(q=0;q<b;q++){let {cell:g,startRow:l}=m[q];l===f?(r=la(m[q].cell.__headerState,w.COLUMN),a.append(z(r).append(v.$createParagraphNode()))):g.setRowSpan(g.__rowSpan+1)}b=d.getChildAtIndex(f);F(b)||H(145);b.insertBefore(a)}};exports.$isTableCellNode=A;exports.$isTableNode=I;exports.$isTableRowNode=F;exports.$isTableSelection=N;exports.$removeTableRowAtIndex=function(a,b){let c=a.getChildren();if(b>=c.length||0>b)throw Error("Expected table cell to be inside of table row.");c[b].remove();return a};
exports.$unmergeCell=function(){var a=v.$getSelection();v.$isRangeSelection(a)||N(a)||H(118);a=a.anchor.getNode();let [b,c,d]=M(a);a=b.__colSpan;let e=b.__rowSpan;if(1<a){for(var k=1;k<a;k++)b.insertAfter(z(w.NO_STATUS));b.setColSpan(1)}if(1<e){let [q,r]=L(d,b,b),{startColumn:g,startRow:l}=r,n;for(k=1;k<e;k++){var f=l+k;let p=q[f];n=(n||c).getNextSibling();F(n)||H(125);var m=null;for(let t=0;t<g;t++){let u=p[t],x=u.cell;u.startRow===f&&(m=x);1<x.__colSpan&&(t+=x.__colSpan-1)}if(null===m)for(m=0;m<
a;m++)ma(n,z(w.NO_STATUS));else for(f=0;f<a;f++)m.insertAfter(z(w.NO_STATUS))}b.setRowSpan(1)}};exports.INSERT_TABLE_COMMAND=ea;exports.TableCellHeaderStates=w;exports.TableCellNode=y;exports.TableNode=Y;exports.TableObserver=ra;exports.TableRowNode=B;
exports.applyTableHandlers=function(a,b,c,d){function e(g){g=a.getCordsFromCellNode(g,f.table);return a.getDOMCellFromCordsOrThrow(g.x,g.y,f.table)}let k=c.getRootElement();if(null===k)throw Error("No root element.");let f=new ra(c,a.getKey()),m=c._window||window;b.__lexicalTableSelection=f;b.addEventListener("mousedown",g=>{setTimeout(()=>{if(0===g.button&&m){var l=sa(g.target);null!==l&&(X(g),f.setAnchorCellForSelection(l));var n=()=>{m.removeEventListener("mouseup",n);m.removeEventListener("mousemove",
p)},p=t=>{const u=sa(t.target);null===u||f.anchorX===u.x&&f.anchorY===u.y||(t.preventDefault(),f.setFocusCellForSelection(u))};m.addEventListener("mouseup",n);m.addEventListener("mousemove",p)}},0)});let q=g=>{0===g.button&&c.update(()=>{const l=v.$getSelection(),n=g.target;N(l)&&l.tableKey===f.tableNodeKey&&k.contains(n)&&f.clearHighlight()})};m.addEventListener("mousedown",q);f.listenersToRemove.add(()=>m.removeEventListener("mousedown",q));f.listenersToRemove.add(c.registerCommand(v.KEY_ARROW_DOWN_COMMAND,
g=>W(c,g,"down",a,f),v.COMMAND_PRIORITY_HIGH));f.listenersToRemove.add(c.registerCommand(v.KEY_ARROW_UP_COMMAND,g=>W(c,g,"up",a,f),v.COMMAND_PRIORITY_HIGH));f.listenersToRemove.add(c.registerCommand(v.KEY_ARROW_LEFT_COMMAND,g=>W(c,g,"backward",a,f),v.COMMAND_PRIORITY_HIGH));f.listenersToRemove.add(c.registerCommand(v.KEY_ARROW_RIGHT_COMMAND,g=>W(c,g,"forward",a,f),v.COMMAND_PRIORITY_HIGH));f.listenersToRemove.add(c.registerCommand(v.KEY_ESCAPE_COMMAND,g=>{var l=v.$getSelection();return N(l)&&(l=h.$findMatchingParent(l.focus.getNode(),
A),A(l))?(X(g),l.selectEnd(),!0):!1},v.COMMAND_PRIORITY_HIGH));let r=g=>()=>{var l=v.$getSelection();if(!V(l,a))return!1;if(N(l))return f.clearText(),!0;if(v.$isRangeSelection(l)){const t=h.$findMatchingParent(l.anchor.getNode(),u=>A(u));if(!A(t))return!1;var n=l.anchor.getNode(),p=l.focus.getNode();n=a.isParentOf(n);p=a.isParentOf(p);if(n&&!p||p&&!n)return f.clearText(),!0;n=(p=h.$findMatchingParent(l.anchor.getNode(),u=>v.$isElementNode(u)))&&h.$findMatchingParent(p,u=>v.$isElementNode(u)&&A(u.getParent()));
if(!v.$isElementNode(n)||!v.$isElementNode(p))return!1;if(g===v.DELETE_LINE_COMMAND&&null===n.getPreviousSibling())return!0;if((g===v.DELETE_CHARACTER_COMMAND||g===v.DELETE_WORD_COMMAND)&&l.isCollapsed()&&0===l.anchor.offset&&p!==n){l=p.getChildren();const u=v.$createParagraphNode();l.forEach(x=>u.append(x));p.replace(u);p.getWritable().__parent=t.getKey();return!0}}return!1};[v.DELETE_WORD_COMMAND,v.DELETE_LINE_COMMAND,v.DELETE_CHARACTER_COMMAND].forEach(g=>{f.listenersToRemove.add(c.registerCommand(g,
r(g),v.COMMAND_PRIORITY_CRITICAL))});b=g=>{const l=v.$getSelection();if(!V(l,a))return!1;if(N(l))return g.preventDefault(),g.stopPropagation(),f.clearText(),!0;v.$isRangeSelection(l)&&(g=h.$findMatchingParent(l.anchor.getNode(),n=>A(n)),A(g));return!1};f.listenersToRemove.add(c.registerCommand(v.KEY_BACKSPACE_COMMAND,b,v.COMMAND_PRIORITY_CRITICAL));f.listenersToRemove.add(c.registerCommand(v.KEY_DELETE_COMMAND,b,v.COMMAND_PRIORITY_CRITICAL));f.listenersToRemove.add(c.registerCommand(v.FORMAT_TEXT_COMMAND,
g=>{let l=v.$getSelection();if(!V(l,a))return!1;if(N(l))return f.formatCells(g),!0;v.$isRangeSelection(l)&&(g=h.$findMatchingParent(l.anchor.getNode(),n=>A(n)),A(g));return!1},v.COMMAND_PRIORITY_CRITICAL));f.listenersToRemove.add(c.registerCommand(v.CONTROLLED_TEXT_INSERTION_COMMAND,()=>{var g=v.$getSelection();if(!V(g,a))return!1;N(g)?f.clearHighlight():v.$isRangeSelection(g)&&(g=h.$findMatchingParent(g.anchor.getNode(),l=>A(l)),A(g));return!1},v.COMMAND_PRIORITY_CRITICAL));d&&f.listenersToRemove.add(c.registerCommand(v.KEY_TAB_COMMAND,
g=>{var l=v.$getSelection();if(!v.$isRangeSelection(l)||!l.isCollapsed()||!V(l,a))return!1;l=Ba(l.anchor.getNode());if(null===l)return!1;X(g);l=a.getCordsFromCellNode(l,f.table);za(f,a,l.x,l.y,g.shiftKey?"backward":"forward");return!0},v.COMMAND_PRIORITY_CRITICAL));f.listenersToRemove.add(c.registerCommand(v.FOCUS_COMMAND,()=>a.isSelected(),v.COMMAND_PRIORITY_HIGH));f.listenersToRemove.add(c.registerCommand(v.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND,g=>{let {nodes:l,selection:n}=g;g=n.getStartEndPoints();
var p=N(n);p=v.$isRangeSelection(n)&&null!==h.$findMatchingParent(n.anchor.getNode(),G=>A(G))&&null!==h.$findMatchingParent(n.focus.getNode(),G=>A(G))||p;if(1!==l.length||!I(l[0])||!p||null===g)return!1;var [t]=g,u=l[0];g=u.getChildren();p=u.getFirstChildOrThrow().getChildrenSize();u=u.getChildrenSize();var x=h.$findMatchingParent(t.getNode(),G=>A(G)),E=(t=x&&h.$findMatchingParent(x,G=>F(G)))&&h.$findMatchingParent(t,G=>I(G));if(!A(x)||!F(t)||!I(E))return!1;var C=t.getIndexWithinParent(),K=Math.min(E.getChildrenSize()-
1,C+u-1);u=x.getIndexWithinParent();x=Math.min(t.getChildrenSize()-1,u+p-1);p=Math.min(u,x);t=Math.min(C,K);u=Math.max(u,x);C=Math.max(C,K);E=E.getChildren();K=0;let R,na;for(x=t;x<=C;x++){var aa=E[x];if(!F(aa))return!1;var ba=g[K];if(!F(ba))return!1;aa=aa.getChildren();ba=ba.getChildren();let G=0;for(let S=p;S<=u;S++){let T=aa[S];if(!A(T))return!1;let wa=ba[G];if(!A(wa))return!1;x===t&&S===p?R=T.getKey():x===C&&S===u&&(na=T.getKey());let Ea=T.getChildren();wa.getChildren().forEach(U=>{v.$isTextNode(U)&&
v.$createParagraphNode().append(U);T.append(U)});Ea.forEach(U=>U.remove());G++}K++}R&&na&&(g=O(),g.set(l[0].getKey(),R,na),v.$setSelection(g));return!0},v.COMMAND_PRIORITY_CRITICAL));f.listenersToRemove.add(c.registerCommand(v.SELECTION_CHANGE_COMMAND,()=>{let g=v.$getSelection(),l=v.$getPreviousSelection();if(v.$isRangeSelection(g)){let {anchor:x,focus:E}=g;var n=x.getNode(),p=E.getNode();n=Ba(n);p=Ba(p);var t=n&&a.is(Ca(n)),u=p&&a.is(Ca(p));let C=t!==u;u=t&&u;t=g.isBackward();C?(n=g.clone(),n.focus.set(a.getKey(),
t?0:a.getChildrenSize(),"element"),v.$setSelection(n),xa(c,f)):u&&!n.is(p)&&(f.setAnchorCellForSelection(e(n)),f.setFocusCellForSelection(e(p),!0))}if(g&&!g.is(l)&&(N(g)||N(l))&&f.tableSelection&&!f.tableSelection.is(l))return N(g)&&g.tableKey===f.tableNodeKey?f.updateTableTableSelection(g):!N(g)&&N(l)&&l.tableKey===f.tableNodeKey&&f.updateTableTableSelection(null),!1;f.hasHijackedSelectionStyles&&!a.isSelected()?ya(c,f):!f.hasHijackedSelectionStyles&&a.isSelected()&&xa(c,f);return!1},v.COMMAND_PRIORITY_CRITICAL));
return f};exports.getDOMCellFromTarget=sa;exports.getTableObserverFromTableElement=function(a){return a.__lexicalTableSelection}

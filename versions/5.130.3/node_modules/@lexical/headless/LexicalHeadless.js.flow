/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */

import type {
  LexicalEditor,
  LexicalNode,
  EditorState,
  EditorThemeClasses,
} from 'lexical';

declare export function createHeadlessEditor(editorConfig?: {
  editorState?: EditorState,
  namespace?: string,
  nodes?: $ReadOnlyArray<Class<LexicalNode>>,
  onError?: (error: Error) => void,
  parentEditor?: LexicalEditor,
  editable?: boolean,
  theme?: EditorThemeClasses,
}): LexicalEditor;

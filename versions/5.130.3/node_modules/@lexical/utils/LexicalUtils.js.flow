/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */
import type {
  EditorState,
  LexicalEditor,
  LexicalNode,
  ElementNode,
} from 'lexical';
export type DFSNode = $ReadOnly<{
  depth: number,
  node: LexicalNode,
}>;
declare export function addClassNamesToElement(
  element: HTMLElement,
  ...classNames: Array<typeof undefined | boolean | null | string>
): void;
declare export function removeClassNamesFromElement(
  element: HTMLElement,
  ...classNames: Array<typeof undefined | boolean | null | string>
): void;
declare export function isMimeType(
  file: File,
  acceptableMimeTypes: Array<string>,
): boolean;
declare export function mediaFileReader(
  files: Array<File>,
  acceptableMimeTypes: Array<string>,
): Promise<Array<$ReadOnly<{file: File, result: string}>>>;
declare export function $dfs(
  startingNode?: LexicalNode,
  endingNode?: LexicalNode,
): Array<DFSNode>;
declare function $getDepth(node: LexicalNode): number;
declare export function $getNearestNodeOfType<T: LexicalNode>(
  node: LexicalNode,
  klass: Class<T>,
): T | null;
export type DOMNodeToLexicalConversion = (element: Node) => LexicalNode;
export type DOMNodeToLexicalConversionMap = {
  [string]: DOMNodeToLexicalConversion,
};
declare export function $findMatchingParent(
  startingNode: LexicalNode,
  findFn: (LexicalNode) => boolean,
): LexicalNode | null;
type Func = () => void;
declare export function mergeRegister(...func: Array<Func>): () => void;
declare export function $getNearestBlockElementAncestorOrThrow(
  startNode: LexicalNode,
): ElementNode;

declare export function registerNestedElementResolver<N: ElementNode>(
  editor: LexicalEditor,
  targetNode: Class<N>,
  cloneNode: (from: N) => N,
  handleOverlap: (from: N, to: N) => void,
): () => void;

declare export function unstable_convertLegacyJSONEditorState(
  editor: LexicalEditor,
  maybeStringifiedEditorState: string,
): EditorState;

declare export function $restoreEditorState(
  editor: LexicalEditor,
  editorState: EditorState,
): void;

declare export function $insertNodeToNearestRoot<T: LexicalNode>(node: T): T;

declare export function $wrapNodeInElement(
  node: LexicalNode,
  createElementNode: () => ElementNode,
): ElementNode;

declare export function $splitNode(
  node: ElementNode,
  offset: number,
): [ElementNode | null, ElementNode];

{"name": "@metascraper/helpers", "description": "Collection of helper functions used by metascraper", "homepage": "https://github.com/microlinkhq/metascraper/packages/metascraper-helpers", "version": "5.45.10", "main": "index.js", "author": {"email": "<EMAIL>", "name": "microlink.io", "url": "https://microlink.io"}, "repository": {"directory": "packages/metascraper-helpers", "type": "git", "url": "git+https://github.com/microlinkhq/metascraper.git#master"}, "bugs": {"url": "https://github.com/microlinkhq/metascraper/issues"}, "keywords": ["helpers", "metascraper"], "dependencies": {"audio-extensions": "0.0.0", "chrono-node": "~2.7.6", "condense-whitespace": "~2.0.0", "data-uri-utils": "~1.0.8", "entities": "~4.5.0", "file-extension": "~4.0.5", "has-values": "~2.0.1", "image-extensions": "~1.1.0", "is-relative-url": "~3.0.0", "is-uri": "~1.2.6", "iso-639-3": "~2.2.0", "isostring": "0.0.1", "jsdom": "~24.1.0", "lodash": "~4.17.21", "memoize-one": "~6.0.0", "microsoft-capitalize": "~1.0.5", "mime": "~3.0.0", "normalize-url": "~6.1.0", "re2": "~1.21.0", "smartquotes": "~2.3.2", "tldts": "~6.1.24", "url-regex-safe": "~4.0.0", "video-extensions": "~1.2.0"}, "devDependencies": {"ava": "5", "cheerio": "latest"}, "engines": {"node": ">= 16"}, "files": ["index.js"], "scripts": {"test": "NODE_PATH=.. TZ=UTC NODE_ENV=test ava --timeout 30s"}, "license": "MIT", "publishConfig": {"access": "public"}, "gitHead": "dba98fa823be9b495ed8935c094d41de6184f578"}
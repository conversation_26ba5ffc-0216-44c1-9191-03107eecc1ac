{"name": "@faker-js/faker", "version": "7.6.0", "description": "Generate massive amounts of fake contextual data", "keywords": ["faker", "faker.js", "fakerjs", "faker-js", "fake data generator", "fake data", "fake-data", "fake-generator", "fake-data-generator", "fake content generator", "fake contextual data generator", "fake contextual data"], "repository": {"type": "git", "url": "https://github.com/faker-js/faker.git"}, "license": "MIT", "main": "dist/cjs/index.js", "module": "dist/esm/index.mjs", "types": "index.d.ts", "typesVersions": {">=4.0": {"*": ["dist/types/*"]}}, "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/cjs/index.js", "import": "./dist/esm/index.mjs"}, "./locale/*": {"types": "./dist/types/locale/*.d.ts", "require": "./dist/cjs/locale/*.js", "import": "./dist/esm/locale/*.mjs"}, "./package.json": "./package.json"}, "files": ["CHANGELOG.md", "CHANGELOG_old.md", "dist", "locale", "tsconfig.json"], "scripts": {"clean": "rimraf coverage .eslintcache dist docs/.vitepress/dist pnpm-lock.yaml node_modules", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:code": "tsx ./scripts/bundle.ts", "build:types": "tsc --emitDeclarationOnly --outDir dist/types", "build": "run-s build:clean build:code build:types", "generate:api-docs": "tsx ./scripts/apidoc.ts", "generate:locales": "tsx ./scripts/generateLocales.ts", "copy:mime-types": "tsx ./scripts/copyMimeTypes.ts", "docs:build": "run-s docs:prepare docs:build:run", "docs:build:run": "vitepress build docs", "docs:build:ci": "run-s build docs:build", "docs:prepare": "run-s generate:api-docs", "docs:dev": "run-s docs:prepare docs:dev:run", "docs:dev:run": "vitepress dev docs", "docs:serve": "vitepress serve docs --port 5173", "format": "prettier --write .", "lint": "eslint --cache --cache-strategy content .", "ts-check:scripts": "tsc --project tsconfig.check-scripts.json", "ts-check:tests": "tsc --project tsconfig.check-tests.json", "test": "vitest", "coverage": "vitest run --coverage", "cypress": "cypress", "docs:test:e2e:ci": "run-s docs:build:ci docs:test:e2e:run", "docs:test:e2e:run": "run-p --race docs:serve \"cypress run\"", "docs:test:e2e:open": "run-p --race docs:serve \"cypress open\"", "release": "standard-version", "prepublishOnly": "pnpm run clean && pnpm install && pnpm run build"}, "simple-git-hooks": {"pre-commit": "npx lint-staged --concurrent false", "commit-msg": "npx tsx scripts/verifyCommit.ts $1"}, "lint-staged": {"*": ["prettier --write --ignore-unknown"], "**/*.ts": ["eslint --ext .ts"]}, "devDependencies": {"@actions/github": "~5.1.1", "@algolia/client-search": "~4.14.2", "@types/glob": "~8.0.0", "@types/markdown-it": "~12.2.3", "@types/node": "~18.8.4", "@types/prettier": "~2.7.1", "@types/react": "~18.0.21", "@types/sanitize-html": "~2.6.2", "@types/validator": "~13.7.7", "@typescript-eslint/eslint-plugin": "~5.40.0", "@typescript-eslint/parser": "~5.40.0", "@vitest/coverage-c8": "~0.24.1", "@vitest/ui": "~0.24.1", "c8": "~7.12.0", "conventional-changelog-cli": "~2.2.2", "cypress": "~10.10.0", "esbuild": "~0.15.10", "eslint": "~8.25.0", "eslint-config-prettier": "~8.5.0", "eslint-define-config": "~1.7.0", "eslint-gitignore": "~0.1.0", "eslint-plugin-jsdoc": "~39.3.6", "eslint-plugin-prettier": "~4.2.1", "glob": "~8.0.3", "lint-staged": "~13.0.3", "mime-db": "~1.52.0", "npm-run-all": "~4.1.5", "picocolors": "~1.0.0", "prettier": "2.7.1", "prettier-plugin-organize-imports": "~3.1.1", "react": "~18.2.0", "react-dom": "~18.2.0", "rimraf": "~3.0.2", "sanitize-html": "~2.7.2", "simple-git-hooks": "~2.8.0", "standard-version": "~9.5.0", "tsx": "~3.10.1", "typedoc": "~0.23.16", "typedoc-plugin-missing-exports": "~1.0.0", "typescript": "~4.8.4", "validator": "~13.7.0", "vite": "~3.1.7", "vitepress": "1.0.0-alpha.19", "vitest": "~0.24.1"}, "packageManager": "pnpm@7.13.4", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}
/**
 * Checks that the given string passes the luhn algorithm.
 *
 * @param str The string to validate.
 */
export declare function luhn<PERSON>heck(str: string): boolean;
/**
 * Calculates the luhn check value for the given string.
 *
 * @param str The string to calculate the check value for.
 * May contain the `L` placeholder at the end.
 */
export declare function luhnCheckValue(str: string): number;

import type { Faker } from '../../..';
/**
 * <PERSON><PERSON>le to generate links to random images on `https://picsum.photos/`.
 */
export declare class LoremPicsum {
    private readonly faker;
    constructor(faker: Faker);
    /**
     * Generates a new picsum image url.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param grayscale Whether to return a grayscale image. Default to `false`.
     * @param blur The optional level of blur to apply. Supports `1` - `10`.
     */
    image(width?: number, height?: number, grayscale?: boolean, blur?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10): string;
    /**
     * Generates a new picsum image url.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param grayscale Whether to return a grayscale image. Default to `false`.
     */
    imageGrayscale(width?: number, height?: number, grayscale?: boolean): string;
    /**
     * Generates a new picsum image url.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param blur The optional level of blur to apply. Supports `1` - `10`.
     */
    imageBlurred(width?: number, height?: number, blur?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10): string;
    /**
     * Generates a new picsum image url.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param grayscale Whether to return a grayscale image. Default to `false`.
     * @param blur The optional level of blur to apply. Supports `1` - `10`.
     * @param seed The optional seed to use.
     */
    imageRandomSeeded(width?: number, height?: number, grayscale?: boolean, blur?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10, seed?: string): string;
    /**
     * Returns a random avatar url.
     *
     * @see faker.internet.avatar()
     *
     * @example
     * faker.internet.avatar()
     * // 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/315.jpg'
     *
     * @deprecated
     */
    avatar(): string;
    /**
     * Generates a new picsum image url.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param grayscale Whether to return a grayscale image. Default to `false`.
     * @param blur The optional level of blur to apply. Supports `1` - `10`.
     * @param seed The optional seed to use.
     */
    imageUrl(width?: number, height?: number, grayscale?: boolean, blur?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10, seed?: string): string;
}

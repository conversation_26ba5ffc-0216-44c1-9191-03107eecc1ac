import type { Faker } from '../..';
/**
 * Module to generate vehicle related entries.
 */
export declare class VehicleModule {
    private readonly faker;
    constructor(faker: Faker);
    /**
     * Returns a random vehicle.
     *
     * @example
     * faker.vehicle.vehicle() // 'BMW Explorer'
     *
     * @since 5.0.0
     */
    vehicle(): string;
    /**
     * Returns a manufacturer name.
     *
     * @example
     * faker.vehicle.manufacturer() // 'Ford'
     *
     * @since 5.0.0
     */
    manufacturer(): string;
    /**
     * Returns a vehicle model.
     *
     * @example
     * faker.vehicle.model() // 'Explorer'
     *
     * @since 5.0.0
     */
    model(): string;
    /**
     * Returns a vehicle type.
     *
     * @example
     * faker.vehicle.type() // 'Coupe'
     *
     * @since 5.0.0
     */
    type(): string;
    /**
     * Returns a fuel type.
     *
     * @example
     * faker.vehicle.fuel() // 'Electric'
     *
     * @since 5.0.0
     */
    fuel(): string;
    /**
     * Returns a vehicle identification number (VIN).
     *
     * @example
     * faker.vehicle.vin() // 'YV1MH682762184654'
     *
     * @since 5.0.0
     */
    vin(): string;
    /**
     * Returns a vehicle color.
     *
     * @example
     * faker.vehicle.color() // 'red'
     *
     * @since 5.0.0
     */
    color(): string;
    /**
     * Returns a vehicle registration number (Vehicle Registration Mark - VRM)
     *
     * @example
     * faker.vehicle.vrm() // 'MF56UPA'
     *
     * @since 5.4.0
     */
    vrm(): string;
    /**
     * Returns a type of bicycle.
     *
     * @example
     * faker.vehicle.bicycle() // 'Adventure Road Bicycle'
     *
     * @since 5.5.0
     */
    bicycle(): string;
}

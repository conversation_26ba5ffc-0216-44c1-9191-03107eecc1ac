import type { Faker } from '../..';
/**
 * Module to generate hacker/IT words and phrases.
 */
export declare class HackerModule {
    private readonly faker;
    constructor(faker: Faker);
    /**
     * Returns a random hacker/IT abbreviation.
     *
     * @example
     * faker.hacker.abbreviation() // 'THX'
     *
     * @since 2.0.1
     */
    abbreviation(): string;
    /**
     * Returns a random hacker/IT adjective.
     *
     * @example
     * faker.hacker.adjective() // 'cross-platform'
     *
     * @since 2.0.1
     */
    adjective(): string;
    /**
     * Returns a random hacker/IT noun.
     *
     * @example
     * faker.hacker.noun() // 'system'
     *
     * @since 2.0.1
     */
    noun(): string;
    /**
     * Returns a random hacker/IT verb.
     *
     * @example
     * faker.hacker.verb() // 'copy'
     *
     * @since 2.0.1
     */
    verb(): string;
    /**
     * Returns a random hacker/IT verb for continuous actions (en: ing suffix; e.g. hacking).
     *
     * @example
     * faker.hacker.ingverb() // 'navigating'
     *
     * @since 2.0.1
     */
    ingverb(): string;
    /**
     * Generates a random hacker/IT phrase.
     *
     * @example
     * faker.hacker.phrase()
     * // 'If we override the card, we can get to the HDD feed through the back-end HDD sensor!'
     *
     * @since 2.0.1
     */
    phrase(): string;
}

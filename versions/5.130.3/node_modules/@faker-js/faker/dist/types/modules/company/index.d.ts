import type { Faker } from '../..';
/**
 * Module to generate company related entries.
 */
export declare class CompanyModule {
    private readonly faker;
    constructor(faker: Faker);
    /**
     * Returns an array with possible company name suffixes.
     *
     * @example
     * faker.company.suffixes() // [ 'Inc', 'and Sons', 'LLC', 'Group' ]
     *
     * @since 2.0.1
     */
    suffixes(): string[];
    /**
     * Generates a random company name.
     *
     * @param format The optional format index used to select a format. Deprecated, do not use.
     *
     * @example
     * faker.company.name() // 'Zieme, Hauck and Mc<PERSON><PERSON>re'
     *
     * @since 7.4.0
     */
    name(format?: number): string;
    /**
     * Generates a random company name.
     *
     * @param format The optional format index used to select a format.
     *
     * @see faker.company.name()
     *
     * @example
     * faker.company.companyName() // 'Zieme, Hauck and Mc<PERSON>lure'
     *
     * @since 2.0.1
     *
     * @deprecated Use `faker.company.name()` instead
     */
    companyName(format?: number): string;
    /**
     * Returns a random company suffix.
     *
     * @example
     * faker.company.companySuffix() // 'and Sons'
     *
     * @since 2.0.1
     */
    companySuffix(): string;
    /**
     * Generates a random business catch phrase.
     *
     * @example
     * faker.company.catchPhrase() // 'Upgradable systematic flexibility'
     *
     * @since 2.0.1
     */
    catchPhrase(): string;
    /**
     * Generates a random company bs phrase.
     *
     * @example
     * faker.company.bs() // 'cultivate synergistic e-markets'
     *
     * @since 2.0.1
     */
    bs(): string;
    /**
     * Returns a random catch phrase adjective.
     *
     * @example
     * faker.company.catchPhraseAdjective() // 'Multi-tiered'
     *
     * @since 2.0.1
     */
    catchPhraseAdjective(): string;
    /**
     * Returns a random catch phrase descriptor.
     *
     * @example
     * faker.company.catchPhraseDescriptor() // 'composite'
     *
     * @since 2.0.1
     */
    catchPhraseDescriptor(): string;
    /**
     * Returns a random catch phrase noun.
     *
     * @example
     * faker.company.catchPhraseNoun() // 'leverage'
     *
     * @since 2.0.1
     */
    catchPhraseNoun(): string;
    /**
     * Returns a random company bs adjective.
     *
     * @example
     * faker.company.bsAdjective() // 'one-to-one'
     *
     * @since 2.0.1
     */
    bsAdjective(): string;
    /**
     * Returns a random company bs buzz word.
     *
     * @example
     * faker.company.bsBuzz() // 'empower'
     *
     * @since 2.0.1
     */
    bsBuzz(): string;
    /**
     * Returns a random company bs noun.
     *
     * @example
     * faker.company.bsNoun() // 'paradigms'
     *
     * @since 2.0.1
     */
    bsNoun(): string;
}

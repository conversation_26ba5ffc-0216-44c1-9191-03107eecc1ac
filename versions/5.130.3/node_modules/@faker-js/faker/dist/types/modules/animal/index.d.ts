import type { Faker } from '../..';
/**
 * Module to generate animal related entries.
 */
export declare class AnimalModule {
    private readonly faker;
    constructor(faker: Faker);
    /**
     * Returns a random dog breed.
     *
     * @example
     * faker.animal.dog() // 'Irish Water Spaniel'
     *
     * @since 5.5.0
     */
    dog(): string;
    /**
     * Returns a random cat breed.
     *
     * @example
     * faker.animal.cat() // 'Singapura'
     *
     * @since 5.5.0
     */
    cat(): string;
    /**
     * Returns a random snake species.
     *
     * @example
     * faker.animal.snake() // 'Eyelash viper'
     *
     * @since 5.5.0
     */
    snake(): string;
    /**
     * Returns a random bear species.
     *
     * @example
     * faker.animal.bear() // 'Asian black bear'
     *
     * @since 5.5.0
     */
    bear(): string;
    /**
     * Returns a random lion species.
     *
     * @example
     * faker.animal.lion() // 'Northeast Congo Lion'
     *
     * @since 5.5.0
     */
    lion(): string;
    /**
     * Returns a random cetacean species.
     *
     * @example
     * faker.animal.cetacean() // 'Spinner Dolphin'
     *
     * @since 5.5.0
     */
    cetacean(): string;
    /**
     * Returns a random horse breed.
     *
     * @example
     * faker.animal.horse() // 'Swedish Warmblood'
     *
     * @since 5.5.0
     */
    horse(): string;
    /**
     * Returns a random bird species.
     *
     * @example
     * faker.animal.bird() // 'Buller's Shearwater'
     *
     * @since 5.5.0
     */
    bird(): string;
    /**
     * Returns a random cow species.
     *
     * @example
     * faker.animal.cow() // 'Brava'
     *
     * @since 5.5.0
     */
    cow(): string;
    /**
     * Returns a random fish species.
     *
     * @example
     * faker.animal.fish() // 'Mandarin fish'
     *
     * @since 5.5.0
     */
    fish(): string;
    /**
     * Returns a random crocodilian species.
     *
     * @example
     * faker.animal.crocodilia() // 'Philippine Crocodile'
     *
     * @since 5.5.0
     */
    crocodilia(): string;
    /**
     * Returns a random insect species.
     *
     * @example
     * faker.animal.insect() // 'Pyramid ant'
     *
     * @since 5.5.0
     */
    insect(): string;
    /**
     * Returns a random rabbit species.
     *
     * @example
     * faker.animal.rabbit() // 'Florida White'
     *
     * @since 5.5.0
     */
    rabbit(): string;
    /**
     * Returns a random rodent breed.
     *
     * @example
     * faker.animal.rodent() // 'Cuscomys ashanika'
     *
     * @since 7.4.0
     */
    rodent(): string;
    /**
     * Returns a random animal type.
     *
     * @example
     * faker.animal.type() // 'crocodilia'
     *
     * @since 5.5.0
     */
    type(): string;
}

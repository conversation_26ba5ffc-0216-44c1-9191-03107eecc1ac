import type { Faker } from '../../..';
/**
 * <PERSON><PERSON><PERSON> to generate links to random images on `https://source.unsplash.com/`.
 */
export declare class Unsplash {
    private readonly faker;
    /**
     * Gets the list of supported unsplash categories.
     *
     * @deprecated
     */
    get categories(): string[];
    constructor(faker: Faker);
    /**
     * Generates a new unsplash image url for a random supported category.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     */
    image(width?: number, height?: number, keyword?: string): string;
    /**
     * Returns a random avatar url.
     *
     * @see faker.internet.avatar()
     *
     * @example
     * faker.internet.avatar()
     * // 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/315.jpg'
     *
     * @deprecated
     */
    avatar(): string;
    /**
     * Generates a new unsplash image url.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param category The category of the image to generate.
     * @param keyword The image keywords to use.
     */
    imageUrl(width?: number, height?: number, category?: string, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "food" category.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     */
    food(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "people" category.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     */
    people(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "nature" category.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     */
    nature(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "technology" category.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     */
    technology(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "objects" category.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     */
    objects(width?: number, height?: number, keyword?: string): string;
    /**
     * Generates a new unsplash image url using the "buildings" category.
     *
     * @param width The width of the image. Defaults to `640`.
     * @param height The height of the image. Defaults to `480`.
     * @param keyword The image keywords to use.
     */
    buildings(width?: number, height?: number, keyword?: string): string;
}

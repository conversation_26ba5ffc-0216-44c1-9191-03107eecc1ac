# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [7.6.0](https://github.com/faker-js/faker/compare/v7.5.0...v7.6.0) (2022-10-12)


### Features

* **finance:** branch code option in bic() ([#1378](https://github.com/faker-js/faker/issues/1378)) ([fe97c29](https://github.com/faker-js/faker/commit/fe97c29ef888b41582d4ad55753c7eb7e4bac6fc))
* **locale:** add first name for zh_CN ([#1376](https://github.com/faker-js/faker/issues/1376)) ([1d8ea82](https://github.com/faker-js/faker/commit/1d8ea82ec8ef7dacc14e747726299fea3e06f6cf))
* **locale:** add male first names starting with Z to id_ID ([#1424](https://github.com/faker-js/faker/issues/1424)) ([f921c14](https://github.com/faker-js/faker/commit/f921c14c5b60eed1742ad79809210597ecdcc98e))
* **locale:** add months, days and few other minor strings to es ([#1320](https://github.com/faker-js/faker/issues/1320)) ([ebb4e2c](https://github.com/faker-js/faker/commit/ebb4e2c37ab08396fcf2d757d1192bfd59e913fc))
* **locale:** add new Indonesia state ([#1400](https://github.com/faker-js/faker/issues/1400)) ([be87581](https://github.com/faker-js/faker/commit/be875818a8cdcf7eeab387b3d9fa232cdb5f7087))
* **locale:** extends id_ID company prefix ([#1399](https://github.com/faker-js/faker/issues/1399)) ([f410564](https://github.com/faker-js/faker/commit/f4105642bf32a8d13bafeaf3228138c984c98b2c))
* lorem null response fix ([#1407](https://github.com/faker-js/faker/issues/1407)) ([a6ce717](https://github.com/faker-js/faker/commit/a6ce71703b02f7c2c4f742106acff05d879c4384))
* **modules:** export module interfaces ([#932](https://github.com/faker-js/faker/issues/932)) ([b9884d0](https://github.com/faker-js/faker/commit/b9884d098d6e14001da36acfba6fbfebdcef8fea))
* **science:** add more units in en locale ([#1386](https://github.com/faker-js/faker/issues/1386)) ([8dcb584](https://github.com/faker-js/faker/commit/8dcb5841f0a6b0ba9eb1b1ebff123118647ab04b))


### Bug Fixes

* **finance:** adjust min length of generated bitcoin address ([#1384](https://github.com/faker-js/faker/issues/1384)) ([10aded2](https://github.com/faker-js/faker/commit/10aded248c5a7aaf57e1b82fad4fa15cfe8adce4))
* **finance:** fix typo in Belarusian Ruble's name ([#1395](https://github.com/faker-js/faker/issues/1395)) ([fe214c3](https://github.com/faker-js/faker/commit/fe214c3c2d4decf469bf3bd381c7f2dfb3fbb3cd))
* **finance:** Update Belarusian Ruble to new version ([#1392](https://github.com/faker-js/faker/issues/1392)) ([526906f](https://github.com/faker-js/faker/commit/526906ffaecd05c48264c30105593341b4da12be))
* **finance:** update Venezuelan bolívar name and code ([#1394](https://github.com/faker-js/faker/issues/1394)) ([0e65143](https://github.com/faker-js/faker/commit/0e65143a5f1c8a25b49dd0e661628a74d1e5a82f))
* **finance:** update Zambian Kwacha to new version ([#1393](https://github.com/faker-js/faker/issues/1393)) ([115e859](https://github.com/faker-js/faker/commit/115e859c0187aa17a5392892fe8b5bbe2ccdcc37))
* **image:** properly lock loremflickr.com images ([#1396](https://github.com/faker-js/faker/issues/1396)) ([2539e6a](https://github.com/faker-js/faker/commit/2539e6af93bd49860776afa57f11b29c2bc4d8f1))
* improve default seed initialization ([#1334](https://github.com/faker-js/faker/issues/1334)) ([925db3a](https://github.com/faker-js/faker/commit/925db3a0194fba6d5984203a69747265848227ef))
* **locale:** change id_ID name to `Indonesian` ([#1401](https://github.com/faker-js/faker/issues/1401)) ([ec0181e](https://github.com/faker-js/faker/commit/ec0181ec699e8687ecabda1d251b80f8d84b3a10))

## [7.5.0](https://github.com/faker-js/faker/compare/v7.4.0...v7.5.0) (2022-08-29)


### Features

* **company:** move name formats to locales ([#1293](https://github.com/faker-js/faker/issues/1293)) ([e1f26a6](https://github.com/faker-js/faker/commit/e1f26a6d2c79f3d9540f1890669631aca0fe5527))
* **datatype:** change hexadecimal signature ([#1238](https://github.com/faker-js/faker/issues/1238)) ([8cb6027](https://github.com/faker-js/faker/commit/8cb6027087fbc3fd038c4063b78f283d9aa48959))
* **locale:** add city names (de) ([#1283](https://github.com/faker-js/faker/issues/1283)) ([cc8b2b2](https://github.com/faker-js/faker/commit/cc8b2b2e1645c5b7cfe647ed64b498dbd277688a))
* **locale:** add directions and directions abbr to pl ([#1225](https://github.com/faker-js/faker/issues/1225)) ([7a718b9](https://github.com/faker-js/faker/commit/7a718b928dc255dc40791025e0b0c0bed6a4d2f9))
* **locale:** add horses to pl ([#1227](https://github.com/faker-js/faker/issues/1227)) ([ca7cb41](https://github.com/faker-js/faker/commit/ca7cb41c0580822da31cd6d4c537d4cd2dcbccb1))
* **locale:** extend Hebrew (he) ([#1257](https://github.com/faker-js/faker/issues/1257)) ([f8c9f60](https://github.com/faker-js/faker/commit/f8c9f60307823be517825ae60624f9bbe2ea5219))
* **name:** add sexType method ([#1289](https://github.com/faker-js/faker/issues/1289)) ([f684a14](https://github.com/faker-js/faker/commit/f684a14ddc3729c74f8434db68324269ae9a640f))
* **name:** extract sex generator from gender to sex ([#1168](https://github.com/faker-js/faker/issues/1168)) ([ad3c9bf](https://github.com/faker-js/faker/commit/ad3c9bf0caab9fac465694641d4d170fa103a243))
* **system:** add cron ([#897](https://github.com/faker-js/faker/issues/897)) ([8fecd58](https://github.com/faker-js/faker/commit/8fecd58b7cfd07826194e0de5d2c868c07c4d913))


### Bug Fixes

* **internal:** fix typo in deprecation message ([#1316](https://github.com/faker-js/faker/issues/1316)) ([7b18404](https://github.com/faker-js/faker/commit/7b184048f3d97ee75db6f5227776a801701d012d))
* **locale:** remove SUS names ([#1303](https://github.com/faker-js/faker/issues/1303)) ([f78843e](https://github.com/faker-js/faker/commit/f78843edb05913c44ebd86535b0d50d22e99fc5e))
* **random:** retry on invalid word generation ([#1307](https://github.com/faker-js/faker/issues/1307)) ([c2108fa](https://github.com/faker-js/faker/commit/c2108fa5db889bb1455a5735934776bcf91fabac))

## [7.4.0](https://github.com/faker-js/faker/compare/v7.3.0...v7.4.0) (2022-08-08)


### Features

* add rodent breed on animal 'en' locale ([#1136](https://github.com/faker-js/faker/issues/1136)) ([a65f1a2](https://github.com/faker-js/faker/commit/a65f1a2b59b909c4b5cc044af560ff91642c9a56))
* **dev:** add male_first_name,female_first_name, jp locale ([#1217](https://github.com/faker-js/faker/issues/1217)) ([ad885ea](https://github.com/faker-js/faker/commit/ad885eaffd4d95031df1b3a09cead7c18ccd2c5b))
* **image:** add image via.placeholder provider ([#1186](https://github.com/faker-js/faker/issues/1186)) ([00d4741](https://github.com/faker-js/faker/commit/00d4741fb8cde9c2790241654ba375fa6afa4f81))
* **locales.en:** extend vehicle ([#1102](https://github.com/faker-js/faker/issues/1102)) ([07a970f](https://github.com/faker-js/faker/commit/07a970f2e05ef4e04a9d7382ab75fa544fb986d6))
* **locales.pl:** add pl translation for gender and binary gender ([#1162](https://github.com/faker-js/faker/issues/1162)) ([22a050e](https://github.com/faker-js/faker/commit/22a050e86fd4fb04e4232a9e57fb1602fd3944cc))
* **system.fileName:** file extension count ([#1101](https://github.com/faker-js/faker/issues/1101)) ([968134c](https://github.com/faker-js/faker/commit/968134c398a11b698b489a492179080aa7ca8c73))
* **system.networkInterface:** add networkInterface faker ([#1133](https://github.com/faker-js/faker/issues/1133)) ([5979f82](https://github.com/faker-js/faker/commit/5979f82e17d4f9adf80fa795afb668d57b33411f))


### Bug Fixes

* **finance.bic:** remove hardcoded elements and simplify function ([#1171](https://github.com/faker-js/faker/issues/1171)) ([5a397e0](https://github.com/faker-js/faker/commit/5a397e0f8a6b4651a5b093b8eafe4895bf166845))

## [7.3.0](https://github.com/faker-js/faker/compare/v7.2.0...v7.3.0) (2022-06-20)

### New & Noteworthy

* Webpack v4 should now be supported 🚀


### Features

* add abbreviations to `hacker.abbreviation()` in the `en` locale ([#1086](https://github.com/faker-js/faker/issues/1086)) ([c5d8934](https://github.com/faker-js/faker/commit/c5d893401be7b9cdf3e54930df0681ea9f43be06))
* improve norwegian translations ([#1042](https://github.com/faker-js/faker/issues/1042)) ([e071c78](https://github.com/faker-js/faker/commit/e071c78df7a40813228aeed0ad54f10471289b3f))
* throw error on unknown locale ([#1071](https://github.com/faker-js/faker/issues/1071)) ([5ea8252](https://github.com/faker-js/faker/commit/5ea8252f727e2e577c2adca9650ac8f24a171632))


### Bug Fixes

* lower target to support Webpack 4 ([#1085](https://github.com/faker-js/faker/issues/1085)) ([080e51d](https://github.com/faker-js/faker/commit/080e51d19afc5c426b804f6afbd44333bbd0d1c9))

## [7.2.0](https://github.com/faker-js/faker/compare/v7.0.1...v7.2.0) (2022-06-06)


### ⚠ SOFT BREAKING CHANGES

* always use https for loremflickr (#1034)
  * we removed the last parameter for `image.imageUrl` and always generate https urls


### Features

* add en_CA city_name (close [#983](https://github.com/faker-js/faker/issues/983)) ([#992](https://github.com/faker-js/faker/issues/992)) ([dc5c720](https://github.com/faker-js/faker/commit/dc5c72070c2dd32e9b1cb32368646fa6ae1bed0c))
* add german adjectives ([#1023](https://github.com/faker-js/faker/issues/1023)) ([1b9a920](https://github.com/faker-js/faker/commit/1b9a920b36133a1b1c6313f1fb086fb697120076))
* always use https for loremflickr ([#1034](https://github.com/faker-js/faker/issues/1034)) ([a235dca](https://github.com/faker-js/faker/commit/a235dcac30064e1357c4b0dc2481b2dcfa04a365))
* science module ([#1014](https://github.com/faker-js/faker/issues/1014)) ([d75d079](https://github.com/faker-js/faker/commit/d75d07970b44bde066de0a765c169809ee8f6b74))


### Bug Fixes

* 🇫🇷 fr and 🇨🇭 fr_CH 🎨 colors ([#1007](https://github.com/faker-js/faker/issues/1007)) ([d96457e](https://github.com/faker-js/faker/commit/d96457ed99fb124dd4fceb16a8b418be2a4f47ed))

## [7.1.0](https://github.com/faker-js/faker/compare/v7.0.1...v7.1.0) (2022-05-25)


### Features

* add en_CA city_name (close [#983](https://github.com/faker-js/faker/issues/983)) ([#992](https://github.com/faker-js/faker/issues/992)) ([dc5c720](https://github.com/faker-js/faker/commit/dc5c72070c2dd32e9b1cb32368646fa6ae1bed0c))
* add music.songName ([#996](https://github.com/faker-js/faker/issues/996)) ([ccd2959](https://github.com/faker-js/faker/commit/ccd2959d72e70b74c7faf755bb690da35e3c48a6))


### Bug Fixes

* Luhn generation algorithms and tests ([#980](https://github.com/faker-js/faker/issues/980)) ([c95826f](https://github.com/faker-js/faker/commit/c95826f348bf317d3cff240a7ebbae4bd80956f6))
* **typescript:** support module Node16/NodeNext ([#1005](https://github.com/faker-js/faker/issues/1005)) ([8736c2a](https://github.com/faker-js/faker/commit/8736c2a0e5370d40e006abfa65c5ce1057c31121))

## [7.0.1](https://github.com/faker-js/faker/compare/v7.0.0...v7.0.1) (2022-05-23)


### Bug Fixes

* revert type exports for module NodeNext, Node16 ([#979](https://github.com/faker-js/faker/issues/979)) ([#989](https://github.com/faker-js/faker/issues/989)) ([c704e36](https://github.com/faker-js/faker/commit/c704e36f692e69bbe23bdf05f4fdcccba4ce8b0f))

## [7.0.0](https://github.com/faker-js/faker/compare/v6.3.1...v7.0.0) (2022-05-23)


### ⚠ BREAKING CHANGES

* remove faker default export (#799)
* target es2020 (#848)
* remove deprecations (#916)
* get rid of export = (#849)
* remove node v12 support (#850)
* reorganize src folder (#909)

### Features

* add casing option ([#955](https://github.com/faker-js/faker/issues/955)) ([4c0e418](https://github.com/faker-js/faker/commit/4c0e41831f8d2fad92f85cea647cbd0873fd842e))
* add date.birthdate ([#962](https://github.com/faker-js/faker/issues/962)) ([5e66d96](https://github.com/faker-js/faker/commit/5e66d9699ba92b42c835662f43d53cba0aadbffb))
* added sponsor and community members to the readme ([#986](https://github.com/faker-js/faker/issues/986)) ([2a6003f](https://github.com/faker-js/faker/commit/2a6003f5fe15b79d361ed4f0d7b3c2b8ec98bfab))
* **address:** use localized fake pattern in city ([#948](https://github.com/faker-js/faker/issues/948)) ([7373a22](https://github.com/faker-js/faker/commit/7373a22f33f38d29ff53e4f4588f0137a35132b8))
* allow banned as string ([#819](https://github.com/faker-js/faker/issues/819)) ([a0d25bb](https://github.com/faker-js/faker/commit/a0d25bbec84c710a6dc8d2cf438af351cf486ab0))
* color module ([#801](https://github.com/faker-js/faker/issues/801)) ([bee6054](https://github.com/faker-js/faker/commit/bee6054f8da67e26dcfdf572103eebabbd6443c0))
* improve and extend 🇫🇷 fr and 🇨🇭fr_CH locales ([#973](https://github.com/faker-js/faker/issues/973)) ([6f39b7c](https://github.com/faker-js/faker/commit/6f39b7cb93baf05863497381a117ad5185948b1e))
* **internet:** HTTP random status code ([#945](https://github.com/faker-js/faker/issues/945)) ([05f555b](https://github.com/faker-js/faker/commit/05f555bc7e304afaa657586ae88f2173507e084f))
* reimplement datatype.bigInt ([#791](https://github.com/faker-js/faker/issues/791)) ([1793385](https://github.com/faker-js/faker/commit/1793385c1ea7b7db349720c7bab20ac9765e9200))
* support locale definitions directly from faker.fake ([#884](https://github.com/faker-js/faker/issues/884)) ([a60d5e3](https://github.com/faker-js/faker/commit/a60d5e3ea3d1109b90cbb51d8a4a10aba2290ada))
* use localized fake pattern in street ([#966](https://github.com/faker-js/faker/issues/966)) ([5af79f4](https://github.com/faker-js/faker/commit/5af79f487bd1537676d017ae09563e53d18458c4))


### Bug Fixes

* empty array passed into helpers.arrayElements ([#921](https://github.com/faker-js/faker/issues/921)) ([100a1ea](https://github.com/faker-js/faker/commit/100a1ea230cba422174a6b5103c56bc4cf9c0dc4))
* **generate:locale:** make the definition types extendible ([#915](https://github.com/faker-js/faker/issues/915)) ([984fbb4](https://github.com/faker-js/faker/commit/984fbb445ff3be3658535bf98916ce5f38943fbf))
* type exports for module NodeNext, Node16 ([#979](https://github.com/faker-js/faker/issues/979)) ([73db3a7](https://github.com/faker-js/faker/commit/73db3a77d95a21e320888228e39ebbf60d551451))
* typo in error message ([#925](https://github.com/faker-js/faker/issues/925)) ([3ea64ce](https://github.com/faker-js/faker/commit/3ea64ceeecfe4cac2f2e6708f12a4bee288d1cb6))


### build

* remove node v12 support ([#850](https://github.com/faker-js/faker/issues/850)) ([cc9aec7](https://github.com/faker-js/faker/commit/cc9aec71eb35e5be4949de9fd6f62f1b8ee48db0))
* get rid of export = ([#849](https://github.com/faker-js/faker/issues/849)) ([20fbeaf](https://github.com/faker-js/faker/commit/20fbeaf35d7c82cdf39da93097927d3a3d81c41c))
* remove deprecations ([#916](https://github.com/faker-js/faker/issues/916)) ([683ee34](https://github.com/faker-js/faker/commit/683ee3405c39408d74d74cac0755a26de7a99e35))
* remove faker default export ([#799](https://github.com/faker-js/faker/issues/799)) ([39b74c0](https://github.com/faker-js/faker/commit/39b74c0326da2d96fa48837a9ad9b995b7158fbd))
* reorganize src folder ([#909](https://github.com/faker-js/faker/issues/909)) ([a2da7c4](https://github.com/faker-js/faker/commit/a2da7c496e9a3741d165ddfe6128b50837fec361))
* target es2020 ([#848](https://github.com/faker-js/faker/issues/848)) ([63f6361](https://github.com/faker-js/faker/commit/63f63612fab40c3def72d9ed50d0ac042a078677))

## [6.3.1](https://github.com/faker-js/faker/compare/v6.3.0...v6.3.1) (2022-05-02)


### Bug Fixes

* replace deprecated arrayElement calls ([#903](https://github.com/faker-js/faker/issues/903)) ([42d6795](https://github.com/faker-js/faker/commit/42d679566624aaedd01eb5c0d9fa54104008016c))

## [6.3.0](https://github.com/faker-js/faker/compare/v6.2.0...v6.3.0) (2022-05-02)


### Features

* add creditCardIssuer ([#888](https://github.com/faker-js/faker/issues/888)) ([58b4f10](https://github.com/faker-js/faker/commit/58b4f10ad11fe42a736dd84f6f61f3c84c743ed8))
* add Hungarian locale ([#53](https://github.com/faker-js/faker/issues/53)) ([9b0d100](https://github.com/faker-js/faker/commit/9b0d1009134d0f0364b0a4851e03fd5e0af145f9))
* configure eol character for git.commitEntry ([#681](https://github.com/faker-js/faker/issues/681)) ([f797b63](https://github.com/faker-js/faker/commit/f797b6310ea73c8ab5637ed415faab221115ea30))
* faker.helpers.maybe ([#874](https://github.com/faker-js/faker/issues/874)) ([a64cbde](https://github.com/faker-js/faker/commit/a64cbde7c9cb05d97617708710c572b23a382f6d))
* random numeric ([#797](https://github.com/faker-js/faker/issues/797)) ([712b1de](https://github.com/faker-js/faker/commit/712b1de126ea6580660a320e065c35ac775f09b3))
* resettable unique store ([#800](https://github.com/faker-js/faker/issues/800)) ([29bba7b](https://github.com/faker-js/faker/commit/29bba7be530d2e11c56de021fc67a9641b2e6e0d))
* return seed value from seed() ([#853](https://github.com/faker-js/faker/issues/853)) ([1851eca](https://github.com/faker-js/faker/commit/1851ecab1e33b6266bb4b4614c814a7674099d01))
* separate methods for object key value ([#503](https://github.com/faker-js/faker/issues/503)) ([36cd461](https://github.com/faker-js/faker/commit/****************************************))


### Bug Fixes

* address.nearbyGPSCoordinate ([#876](https://github.com/faker-js/faker/issues/876)) ([3e23fc4](https://github.com/faker-js/faker/commit/3e23fc48820319c3e6b8d0581c70d836401f88ba))
* dont log deprecations on startup ([#857](https://github.com/faker-js/faker/issues/857)) ([a28b5de](https://github.com/faker-js/faker/commit/a28b5deab9079c567b7eb8a1917c661cadd35849))

## [6.2.0](https://github.com/faker-js/faker/compare/v6.1.2...v6.2.0) (2022-04-21)


### Features

* add chinese hyphenated name ([#277](https://github.com/faker-js/faker/issues/277)) ([40c9d5a](https://github.com/faker-js/faker/commit/40c9d5a8d0a03d8cb9275e40640b6d193ce1780c))
* add internet.emoji ([#504](https://github.com/faker-js/faker/issues/504)) ([cb746cb](https://github.com/faker-js/faker/commit/cb746cb466743a219c0e3845edb29527a06b0a35))
* add Lastname - Bhadresha, Jangid, Suthar ([#243](https://github.com/faker-js/faker/issues/243)) ([6214425](https://github.com/faker-js/faker/commit/6214425d92a2d34268283bace19a02d6f1d9924e))
* Add MongoDB ObjectId generation ([#616](https://github.com/faker-js/faker/issues/616)) ([a5b3888](https://github.com/faker-js/faker/commit/a5b38885f7b8c420b3587d8ded7fc5d180c92ed7))
* add more arabic names dataset ([#655](https://github.com/faker-js/faker/issues/655)) ([b3a9abc](https://github.com/faker-js/faker/commit/b3a9abcf487372f53eece207099f3f645f9b9c8f))
* add more arabic names datasets ([#368](https://github.com/faker-js/faker/issues/368)) ([f824f9d](https://github.com/faker-js/faker/commit/f824f9d231b0e48f98f84b8cac71e79566f4f5e7))
* add os. as street prefix for pl ([#640](https://github.com/faker-js/faker/issues/640)) ([c6d0cc7](https://github.com/faker-js/faker/commit/c6d0cc7ae4f8ea220803bcee5a5e84a7b8230951))
* add some en locale city prefixes and suffixes ([#685](https://github.com/faker-js/faker/issues/685)) ([955ea43](https://github.com/faker-js/faker/commit/955ea43c2b885feb276a670babe859cb3ecc87d2))
* add some en locale product names ([#686](https://github.com/faker-js/faker/issues/686)) ([f22b93a](https://github.com/faker-js/faker/commit/f22b93a0d6fc08e122b67c2a7dd010ee81d67be4))
* append more korean dataset and delete wrong dataset ([#573](https://github.com/faker-js/faker/issues/573)) ([acaedb3](https://github.com/faker-js/faker/commit/acaedb30d2ff47c8597d08db5bf03f487bfcb153))
* dynamic definitions tree ([#822](https://github.com/faker-js/faker/issues/822)) ([069f4d1](https://github.com/faker-js/faker/commit/069f4d1c08b2a314aa24a4a55b7498ff97be7c3a))
* extend list of domain suffix for PL locale ([#266](https://github.com/faker-js/faker/issues/266)) ([5beac4b](https://github.com/faker-js/faker/commit/5beac4be3ebcd2bc6096f6ed2988353bb592aa26))
* extend list of street prefixes for pl locale ([#844](https://github.com/faker-js/faker/issues/844)) ([425accd](https://github.com/faker-js/faker/commit/425accdeac2973a4111ea419af31a52b522b1918))
* faker.finance.pin() ([#695](https://github.com/faker-js/faker/issues/695)) ([20f33e6](https://github.com/faker-js/faker/commit/20f33e6640551b1d95059207ae2a54ba9115690c))
* immutable options in random.alpha methods ([#790](https://github.com/faker-js/faker/issues/790)) ([dd11846](https://github.com/faker-js/faker/commit/dd11846bd9c61cc09917a06ec231592fff3ec653))
* phone IMEI ([#829](https://github.com/faker-js/faker/issues/829)) ([c25ecd0](https://github.com/faker-js/faker/commit/c25ecd08ec57fae5967148bee14fec1c4be99472))
* special characters in emails ([#792](https://github.com/faker-js/faker/issues/792)) ([3b5a21f](https://github.com/faker-js/faker/commit/3b5a21f3aae52f263f2c91e763fcee613092166c))
* updated mime-db to 1.52.0 ([#808](https://github.com/faker-js/faker/issues/808)) ([78a30fb](https://github.com/faker-js/faker/commit/78a30fbdb8779a0e4b242d353a696672f64a1fcc))
* use street address templates from locales ([#754](https://github.com/faker-js/faker/issues/754)) ([b04dc91](https://github.com/faker-js/faker/commit/b04dc914eec44ec3c5ae760976b616ce1d2584e1))


### Bug Fixes

* Maryland (MD) wrong postal code ([#804](https://github.com/faker-js/faker/issues/804)) ([4ac2a04](https://github.com/faker-js/faker/commit/4ac2a0424fffcf48dedea58148ac1b4b510a68a5))
* use `\n` as default separator for lorem.paragraphs() ([#783](https://github.com/faker-js/faker/issues/783)) ([6a2d8fc](https://github.com/faker-js/faker/commit/6a2d8fce89e0b46f7b9693a7b1f697c108e8af04))

## [6.1.2](https://github.com/faker-js/faker/compare/v6.1.1...v6.1.2) (2022-04-04)


### Features

* FakerError ([#718](https://github.com/faker-js/faker/issues/718)) ([c3be3b1](https://github.com/faker-js/faker/commit/c3be3b1945248ed422342b046ad765d252bbac05))


### Bug Fixes

* datatype.number when min = max + precision, throw when max > min ([#664](https://github.com/faker-js/faker/issues/664)) ([0304120](https://github.com/faker-js/faker/commit/03041201c21ad599bbe1874c375f4f41b94961ba))
* deterministic results for address.nearbyGPSCoordinate ([#737](https://github.com/faker-js/faker/issues/737)) ([bc7bd57](https://github.com/faker-js/faker/commit/bc7bd571d8d6c70b046a3bda23c61c527ddb1d4a))
* random word fails on undefined ([#771](https://github.com/faker-js/faker/issues/771)) ([fb1b87e](https://github.com/faker-js/faker/commit/fb1b87e2249798c6257cb5383f73a15022f438f0))
* remove doubled extension in system.filePath ([#300](https://github.com/faker-js/faker/issues/300)) ([2532eb9](https://github.com/faker-js/faker/commit/2532eb9f5a6f73bbc1786ec91952f838d98fd72a))

## [6.1.1](https://github.com/faker-js/faker/compare/v6.1.0...v6.1.1) (2022-03-28)

### Bug Fixes

* forgot to run `pnpm run build`

## [6.1.0](https://github.com/faker-js/faker/compare/v6.0.0-alpha.6...v6.1.0) (2022-03-28)

⚠**WARNING**: This build is broken due to missing build files. Please don't use this version!

### Features

* **types:** generate all locales index files with non-any types ([#494](https://github.com/faker-js/faker/issues/494)) ([b611ca1](https://github.com/faker-js/faker/commit/b611ca1c6f7d756f224a1a7b641e36b531d169be))
* **types:** provide strong typing for locales 2 ([#398](https://github.com/faker-js/faker/issues/398)) ([419e9e2](https://github.com/faker-js/faker/commit/419e9e20a570b44909faf24c9019bf7f232ee7a9))


### Bug Fixes

* accept dates as params for Date methods ([#200](https://github.com/faker-js/faker/issues/200)) ([91a1aab](https://github.com/faker-js/faker/commit/91a1aaba954b7d172c3dd3346567078644b74189))
* alphaNumeric all chars banned ([#550](https://github.com/faker-js/faker/issues/550)) ([c51fb15](https://github.com/faker-js/faker/commit/c51fb1570669284e14915916636de97b7f644e17))
* corrected the Costa Rican IBAN format ([#646](https://github.com/faker-js/faker/issues/646)) ([3f3de78](https://github.com/faker-js/faker/commit/3f3de78c83ae919fd44531ac7ae9caed885800d4))
* correcting the readme file ([#529](https://github.com/faker-js/faker/issues/529)) ([6c9dcdd](https://github.com/faker-js/faker/commit/6c9dcdd03050ef70554fc2bb6ccc0c883fd1edc9))
* datatype.datetime should use static boundaries ([#343](https://github.com/faker-js/faker/issues/343)) ([7141cd7](https://github.com/faker-js/faker/commit/7141cd7d8a2fd505d3338d13ca29fd1ab7a5dc80))
* enable algolia search ([#641](https://github.com/faker-js/faker/issues/641)) ([a759c87](https://github.com/faker-js/faker/commit/a759c8707c2e3758e64e1c174c2de7aad51cd64b))
* fake behavior with special replacement patterns ([#688](https://github.com/faker-js/faker/issues/688)) ([8d1aefb](https://github.com/faker-js/faker/commit/8d1aefbda070265909cedb07af564ea143be74a7))
* fake is unable to return empty strings ([#347](https://github.com/faker-js/faker/issues/347)) ([301a6d2](https://github.com/faker-js/faker/commit/301a6d2024816bf40f1091ccffe6bb81cb7ba7b0))
* **finance:** update iso-3166-1 country codes for IBAN/BIC ([#168](https://github.com/faker-js/faker/issues/168)) ([6050d7a](https://github.com/faker-js/faker/commit/6050d7acbb991568cbdac7b0c16a088aef200abc))
* fix bicycle_types files being named incorrectly ([#477](https://github.com/faker-js/faker/issues/477)) ([7a2a522](https://github.com/faker-js/faker/commit/7a2a522c181dc696f6e9b4767ca18d39bad4476d))
* fix time.recent method signature ([#586](https://github.com/faker-js/faker/issues/586)) ([60d3cc5](https://github.com/faker-js/faker/commit/60d3cc519cd2cab061a23fbe09164d8b59e177ff))
* fix unique method types ([#457](https://github.com/faker-js/faker/issues/457)) ([14df7d3](https://github.com/faker-js/faker/commit/14df7d3f70b54c4a153f5dcf111ef90575bbbe9e))
* force passed locales into faker constructor ([#580](https://github.com/faker-js/faker/issues/580)) ([5ed963f](https://github.com/faker-js/faker/commit/5ed963f1e4928eb2df36f230faf7f9d63b51bef1))
* handle missing or broken locales main index files ([#478](https://github.com/faker-js/faker/issues/478)) ([ff97431](https://github.com/faker-js/faker/commit/ff974319a63acbcbbb96cf1a67a48616de9fd9da))
* mersenne rand invalid input argument ([#577](https://github.com/faker-js/faker/issues/577)) ([486c76e](https://github.com/faker-js/faker/commit/486c76e34f22cf1fd66fa2c99e605d52c7077760))
* move el credit card information to the expected location ([#484](https://github.com/faker-js/faker/issues/484)) ([dcbd18e](https://github.com/faker-js/faker/commit/dcbd18e13ab53e837de17cec7b3f8e51ba479d0e))
* name module gender ([#644](https://github.com/faker-js/faker/issues/644)) ([7675abe](https://github.com/faker-js/faker/commit/7675abe5a62c8d44ecfd4a6f50bfb5fa50f40efa))
* normalize provider in finance.creditCardNumber ([#662](https://github.com/faker-js/faker/issues/662)) ([9ce1551](https://github.com/faker-js/faker/commit/9ce1551ab7c9cafc97e3d051f0632591e34eb3ce))
* only return word with desirable alpha characters ([#654](https://github.com/faker-js/faker/issues/654)) ([b7b2e4f](https://github.com/faker-js/faker/commit/b7b2e4f8dbd40b6bb2678fb60ee95e198838d08a))
* remove invalid aliases ([#488](https://github.com/faker-js/faker/issues/488)) ([58fbfe3](https://github.com/faker-js/faker/commit/58fbfe3835b0f27949e7eb262d246eb9d84bb870))
* remove unreachable code finance ([#508](https://github.com/faker-js/faker/issues/508)) ([1bc622a](https://github.com/faker-js/faker/commit/1bc622a9499bf236052809610ecabe80f87de482))
* replaced placeimg.com on loremflickr.com ([#692](https://github.com/faker-js/faker/issues/692)) ([2a49e82](https://github.com/faker-js/faker/commit/2a49e8215a81ef284a2955f7f9a51d6f2bd76f5f))
* solve various todos ([#649](https://github.com/faker-js/faker/issues/649)) ([d0a473f](https://github.com/faker-js/faker/commit/d0a473f7ca6b525391ca8ab17e029335c66ed99e))
* test random.alphaNumeric ([#517](https://github.com/faker-js/faker/issues/517)) ([41ec6f0](https://github.com/faker-js/faker/commit/41ec6f08784afc2a674568328a0ea2bfb033a411))
* use require/import export map in package.json ([#697](https://github.com/faker-js/faker/issues/697)) ([0f74908](https://github.com/faker-js/faker/commit/0f74908a5ac16d4a9b73d747a275a9399a131757))
* Vehicle vin is always 17 characters long ([#320](https://github.com/faker-js/faker/issues/320)) ([d2fc1e6](https://github.com/faker-js/faker/commit/d2fc1e6b5ba55242d16b9b8a1e9f42c7b24957b0))

## [6.0.0](https://github.com/faker-js/faker/compare/v6.0.0-beta.0...v6.0.0) (2022-03-15)


### Bug Fixes

* fix some lint warnings ([#613](https://github.com/faker-js/faker/issues/613)) ([5cb74b1b](https://github.com/faker-js/faker/commit/5cb74b1bf31f44311b4ee54ea320b81f68879f07))
* **docs:** add image_providers jsdocs ([#612](https://github.com/faker-js/faker/issues/612)) ([09487b6b](https://github.com/faker-js/faker/commit/09487b6b3a6e6cc3de0303851b9913ecdf1390dc))
* **docs:** improve visualization for complex types ([#608](https://github.com/faker-js/faker/issues/608)) ([f038937c](https://github.com/faker-js/faker/commit/f038937c973a0ad4b0014c5aaa56a0323c94fff4))
* configure eqeqeq lint rule ([#595](https://github.com/faker-js/faker/issues/595)) ([5cd3daef](https://github.com/faker-js/faker/commit/5cd3daef2b586b7f7c89b82259831ca4810d77d5))
* fix eslint error ([#597](https://github.com/faker-js/faker/issues/597)) ([4f7447c3](https://github.com/faker-js/faker/commit/4f7447c3e38da71d261da254dd631b84fb4c22c4))
* **docs:** comment out algolia until search is available ([#599](https://github.com/faker-js/faker/issues/599)) ([bcc97d69](https://github.com/faker-js/faker/commit/bcc97d69ba434b436d4b173dfba8122a417e8345))
* **docs:** add test for api docs generation ([#574](https://github.com/faker-js/faker/issues/574)) ([c99160f0](https://github.com/faker-js/faker/commit/c99160f0ab059729af29d7ee08bd97c38d323b2a))
* **docs:** display correct signature ([#596](https://github.com/faker-js/faker/issues/596)) ([c115056e](https://github.com/faker-js/faker/commit/c115056e04d1e42f97c8d77daed3d9056c375953))
* **docs:** show union options parameters ([#602](https://github.com/faker-js/faker/issues/602)) ([5595b9fc](https://github.com/faker-js/faker/commit/5595b9fc7bf700cb0ca3d0792a3a879110ba43c5))

## [6.0.0-beta.0](https://github.com/faker-js/faker/compare/v6.0.0-alpha.6...v6.0.0-beta.0) (2022-03-07)


### Features

* **types:** generate all locales index files with non-any types ([#494](https://github.com/faker-js/faker/issues/494)) ([b611ca1](https://github.com/faker-js/faker/commit/b611ca1c6f7d756f224a1a7b641e36b531d169be))
* **types:** provide strong typing for locales 2 ([#398](https://github.com/faker-js/faker/issues/398)) ([419e9e2](https://github.com/faker-js/faker/commit/419e9e20a570b44909faf24c9019bf7f232ee7a9))


### Bug Fixes

* correcting the readme file ([#529](https://github.com/faker-js/faker/issues/529)) ([6c9dcdd](https://github.com/faker-js/faker/commit/6c9dcdd03050ef70554fc2bb6ccc0c883fd1edc9))
* fix bicycle_types files being named incorrectly ([#477](https://github.com/faker-js/faker/issues/477)) ([7a2a522](https://github.com/faker-js/faker/commit/7a2a522c181dc696f6e9b4767ca18d39bad4476d))
* fix time.recent method signature ([#586](https://github.com/faker-js/faker/issues/586)) ([60d3cc5](https://github.com/faker-js/faker/commit/60d3cc519cd2cab061a23fbe09164d8b59e177ff))
* fix unique method types ([#457](https://github.com/faker-js/faker/issues/457)) ([14df7d3](https://github.com/faker-js/faker/commit/14df7d3f70b54c4a153f5dcf111ef90575bbbe9e))
* handle missing or broken locales main index files ([#478](https://github.com/faker-js/faker/issues/478)) ([ff97431](https://github.com/faker-js/faker/commit/ff974319a63acbcbbb96cf1a67a48616de9fd9da))
* move el credit card information to the expected location ([#484](https://github.com/faker-js/faker/issues/484)) ([dcbd18e](https://github.com/faker-js/faker/commit/dcbd18e13ab53e837de17cec7b3f8e51ba479d0e))
* remove invalid aliases ([#488](https://github.com/faker-js/faker/issues/488)) ([58fbfe3](https://github.com/faker-js/faker/commit/58fbfe3835b0f27949e7eb262d246eb9d84bb870))
* test random.alphaNumeric ([#517](https://github.com/faker-js/faker/issues/517)) ([41ec6f0](https://github.com/faker-js/faker/commit/41ec6f08784afc2a674568328a0ea2bfb033a411))

## [6.0.0-alpha.7](https://github.com/faker-js/faker/compare/v6.0.0-alpha.6...v6.0.0-alpha.7) (2022-02-20)


### Features

* **types:** generate all locales index files with non-any types ([#494](https://github.com/faker-js/faker/issues/494)) ([b611ca1](https://github.com/faker-js/faker/commit/b611ca1c6f7d756f224a1a7b641e36b531d169be))
* **types:** provide strong typing for locales 2 ([#398](https://github.com/faker-js/faker/issues/398)) ([419e9e2](https://github.com/faker-js/faker/commit/419e9e20a570b44909faf24c9019bf7f232ee7a9))


### Bug Fixes

* fix bicycle_types files being named incorrectly ([#477](https://github.com/faker-js/faker/issues/477)) ([7a2a522](https://github.com/faker-js/faker/commit/7a2a522c181dc696f6e9b4767ca18d39bad4476d))
* fix unique method types ([#457](https://github.com/faker-js/faker/issues/457)) ([14df7d3](https://github.com/faker-js/faker/commit/14df7d3f70b54c4a153f5dcf111ef90575bbbe9e))
* handle missing or broken locales main index files ([#478](https://github.com/faker-js/faker/issues/478)) ([ff97431](https://github.com/faker-js/faker/commit/ff974319a63acbcbbb96cf1a67a48616de9fd9da))
* move el credit card information to the expected location ([#484](https://github.com/faker-js/faker/issues/484)) ([dcbd18e](https://github.com/faker-js/faker/commit/dcbd18e13ab53e837de17cec7b3f8e51ba479d0e))
* remove invalid aliases ([#488](https://github.com/faker-js/faker/issues/488)) ([58fbfe3](https://github.com/faker-js/faker/commit/58fbfe3835b0f27949e7eb262d246eb9d84bb870))
* test random.alphaNumeric ([#517](https://github.com/faker-js/faker/issues/517)) ([41ec6f0](https://github.com/faker-js/faker/commit/41ec6f08784afc2a674568328a0ea2bfb033a411))
* **build:** add redirect aliases for cjs ([#520](https://github.com/faker-js/faker/issues/520)) ([7d806d9](https://github.com/faker-js/faker/commit/7d806d9a0b9914f9e0ce0c11e529bdd8ae116fc4))
* **docs:** various fixes ([#523](https://github.com/faker-js/faker/issues/523)) ([b22cf3b](https://github.com/faker-js/faker/commit/b22cf3b809efab92f5933b9f6641e1959a4c8fcc))

## [6.0.0-alpha.6](https://github.com/faker-js/faker/compare/v6.0.0-alpha.5...v6.0.0-alpha.6) (2022-02-10)


### Features

* add chat.fakerjs.dev subdomain redirection to discord ([#306](https://github.com/faker-js/faker/issues/306)) ([32b4839](https://github.com/faker-js/faker/commit/32b483920450a5c1a084e83723f1c433db8ff34f))
* auto generate/update locale files ([#252](https://github.com/faker-js/faker/issues/252)) ([df48704](https://github.com/faker-js/faker/commit/df48704c818a1addb8c32140f12e592e243f4e6d))
* **types:** provide strong typing for locales ([#363](https://github.com/faker-js/faker/issues/363)) ([5e6754d](https://github.com/faker-js/faker/commit/5e6754da61b63019fd063fad26adbeeabd8b789b))


### Bug Fixes

* improve internet tests and fix bug in domain name generation ([#258](https://github.com/faker-js/faker/issues/258)) ([c6f7aa9](https://github.com/faker-js/faker/commit/c6f7aa9de0259b45a62bf59336c3ff037a40651d))
* optional args on faker.finance.iban() ([#431](https://github.com/faker-js/faker/issues/431)) ([c71469c](https://github.com/faker-js/faker/commit/c71469c8e0bca9ccf4a8f66be1294f5139f9631f))
* param can be optional ([#381](https://github.com/faker-js/faker/issues/381)) ([f8a95a1](https://github.com/faker-js/faker/commit/f8a95a1b2d66d188b5f5ef622de7e2c7c27743bc))
* remove redundant precision from datatype.datetime options ([#335](https://github.com/faker-js/faker/issues/335)) ([9d5a7a2](https://github.com/faker-js/faker/commit/9d5a7a2533c569fa1cad2dacb6ae223644bb98cb))
* removed dead code and adjusted fileExt ([#304](https://github.com/faker-js/faker/issues/304)) ([471bc1d](https://github.com/faker-js/faker/commit/471bc1d5d797b5eee5de6c980505d0d8db158776))
* removed node v12 from the pipe ([#309](https://github.com/faker-js/faker/issues/309)) ([537f56e](https://github.com/faker-js/faker/commit/537f56e129512b38f62f802a8099dc40f5cfa66f))
* some return types ([#307](https://github.com/faker-js/faker/issues/307)) ([4ca61ca](https://github.com/faker-js/faker/commit/4ca61ca5b73c3cd4a0ba4094aabd492e2b7f74bf))

## [6.0.0-alpha.5](https://github.com/faker-js/faker/compare/v6.0.0-alpha.4...v6.0.0-alpha.5) (2022-01-25)


### Bug Fixes

* revert pnpm only-allow ([#291](https://github.com/faker-js/faker/issues/291)) ([132e8c3](https://github.com/faker-js/faker/commit/132e8c3d7c173a6c9708f865e8c7d6c31f9c6e55))

## [6.0.0-alpha.4](https://github.com/faker-js/faker/compare/v6.0.0-alpha.3...v6.0.0-alpha.4) (2022-01-24)


### ⚠ BREAKING CHANGES

* support tree-shaking (#152)

### Features

* add autocomplete support for locales ([#248](https://github.com/faker-js/faker/issues/248)) ([c1f2b09](https://github.com/faker-js/faker/commit/c1f2b09be957760a133baaab9d58364bda57be0d))
* migrate locales to typescript ([#238](https://github.com/faker-js/faker/issues/238)) ([d4cfa3c](https://github.com/faker-js/faker/commit/d4cfa3cbbc1c887a17dddbdd5ffc64140e8fa605))
* support tree-shaking ([#152](https://github.com/faker-js/faker/issues/152)) ([ccf119d](https://github.com/faker-js/faker/commit/ccf119deeb6c6d4c942544d83b515cddc5681ba3))
* use export default for locales ([#249](https://github.com/faker-js/faker/issues/249)) ([93e8e53](https://github.com/faker-js/faker/commit/93e8e535ba5bf85fae029078941523ce566fb356))


### Bug Fixes

* add missing fakers for existing locales ([#263](https://github.com/faker-js/faker/issues/263)) ([7177d37](https://github.com/faker-js/faker/commit/7177d375ec1d29d5bcab7525ef90a3eed1a4fa84))
* jalapeño typo ([#259](https://github.com/faker-js/faker/issues/259)) ([74204a0](https://github.com/faker-js/faker/commit/74204a0ded997b08a988fe20481a715081b7daf6))
* ts support for locales ([#169](https://github.com/faker-js/faker/issues/169)) ([61d0296](https://github.com/faker-js/faker/commit/61d02960a9132fa2c4d4d7259883b2e944233bb9))
* **types:** fixed datetime types for optional argument ([#192](https://github.com/faker-js/faker/issues/192)) ([60b71fd](https://github.com/faker-js/faker/commit/60b71fd257c2b95a6997f5aac516b88327a78f4b))
* **types:** fixed exampleEmail arguments types ([#241](https://github.com/faker-js/faker/issues/241)) ([66aa374](https://github.com/faker-js/faker/commit/66aa374fc3ca958070819d7eb6b119accc01cd3d))
* typo in update.md ([#180](https://github.com/faker-js/faker/issues/180)) ([4a69d6d](https://github.com/faker-js/faker/commit/4a69d6dc295153e037c15e084e55aef1e662096b))

## [6.0.0-alpha.3](https://github.com/faker-js/faker/compare/v6.0.0-alpha.2...v6.0.0-alpha.3) (2022-01-15)


### Features

* add definitions ([#84](https://github.com/faker-js/faker/issues/84)) ([cfdfad5](https://github.com/faker-js/faker/commit/cfdfad5d77b9b52a07c02d2fb3ca4588acdb7d5d))
* **domain_suffix:** add the `id`, `ponpes.id` and `net.id` domains for id_ID ([#137](https://github.com/faker-js/faker/issues/137)) ([ce4d48e](https://github.com/faker-js/faker/commit/ce4d48eb300e9e5e87ad9d7dcf7522e3147c98fb))
* migrate address ([#133](https://github.com/faker-js/faker/issues/133)) ([88afa60](https://github.com/faker-js/faker/commit/88afa60f739a60f08efe7365a3b80dbfd306055a))
* migrate animal ([#105](https://github.com/faker-js/faker/issues/105)) ([0abec83](https://github.com/faker-js/faker/commit/0abec83219a3e070ef9517849df556cad609ec4d))
* migrate commerce ([#106](https://github.com/faker-js/faker/issues/106)) ([82ab145](https://github.com/faker-js/faker/commit/82ab145286909d49a798c95bf46ea504ebdd7be7))
* migrate company ([#132](https://github.com/faker-js/faker/issues/132)) ([0205183](https://github.com/faker-js/faker/commit/0205183ed821fa1bc04bbb290e7ab713db6e5a91))
* migrate database ([#89](https://github.com/faker-js/faker/issues/89)) ([4d4653e](https://github.com/faker-js/faker/commit/4d4653e6cdfd551b5d6d7a939c85f4232391a235))
* migrate date ([#83](https://github.com/faker-js/faker/issues/83)) ([0fe6f2b](https://github.com/faker-js/faker/commit/0fe6f2b2f6780497c8992820326a610a702eb8aa))
* migrate fake ([#79](https://github.com/faker-js/faker/issues/79)) ([8fa14c6](https://github.com/faker-js/faker/commit/8fa14c698c25ee23ffc5168d7c69dc37fd7f8c4b))
* migrate finance ([#131](https://github.com/faker-js/faker/issues/131)) ([a1208ca](https://github.com/faker-js/faker/commit/a1208cacf76f96738a3505e2a71ae91d9759b805))
* migrate git ([#78](https://github.com/faker-js/faker/issues/78)) ([6122d3c](https://github.com/faker-js/faker/commit/6122d3c9b0e6c352514bf35707187da56e379359))
* migrate hacker ([#81](https://github.com/faker-js/faker/issues/81)) ([e0005ad](https://github.com/faker-js/faker/commit/e0005addb5c21773e0f61b2ba8be821b94822a10))
* migrate helpers ([#85](https://github.com/faker-js/faker/issues/85)) ([07f8b44](https://github.com/faker-js/faker/commit/07f8b4482eec8da397f87bbaa81450f7036f2236))
* migrate image ([#92](https://github.com/faker-js/faker/issues/92)) ([3c3e567](https://github.com/faker-js/faker/commit/3c3e567f4d9d901770a76bf30068a6742a00d882))
* migrate internet ([#94](https://github.com/faker-js/faker/issues/94)) ([8fcfcc6](https://github.com/faker-js/faker/commit/8fcfcc6b1a64f078ad14b4a434ffb2969487aca1))
* migrate lorem ([#86](https://github.com/faker-js/faker/issues/86)) ([7e6273b](https://github.com/faker-js/faker/commit/7e6273b32bdb6a1f1057a2d9b7dd1bbe6acccca0))
* migrate music ([#107](https://github.com/faker-js/faker/issues/107)) ([46d51ba](https://github.com/faker-js/faker/commit/46d51bac072e1efee0b7c6ddfa4b6aac2a9aa0ee))
* migrate name ([#103](https://github.com/faker-js/faker/issues/103)) ([de9f9d6](https://github.com/faker-js/faker/commit/de9f9d67c5fe9a698b048644f5b7374142b523b6))
* migrate phone ([#127](https://github.com/faker-js/faker/issues/127)) ([77f4e63](https://github.com/faker-js/faker/commit/77f4e63c4bfac731b813f7577a6e257548e20dd9))
* migrate random ([#91](https://github.com/faker-js/faker/issues/91)) ([efc11f1](https://github.com/faker-js/faker/commit/efc11f16e9c05e144c93fd2a6192c22313737918))
* migrate system ([#90](https://github.com/faker-js/faker/issues/90)) ([c0a6277](https://github.com/faker-js/faker/commit/c0a62778cc32b120e256f9779b1088b8a753e2ae))
* migrate time ([#74](https://github.com/faker-js/faker/issues/74)) ([168a211](https://github.com/faker-js/faker/commit/168a21146f953b3e219c32bd91bff3da9fe92296))
* migrate unique ([#128](https://github.com/faker-js/faker/issues/128)) ([86580d8](https://github.com/faker-js/faker/commit/86580d89135bfa0c077d96bb9634a1e47c2f7ea9))
* migrate vehicle ([#130](https://github.com/faker-js/faker/issues/130)) ([661f3b4](https://github.com/faker-js/faker/commit/661f3b4272b2a429c704ab31b4e839bd9ac94f94))
* migrate word ([#102](https://github.com/faker-js/faker/issues/102)) ([1b10032](https://github.com/faker-js/faker/commit/1b1003269ac90db163d4a2c23f5160192add8d6d))
* rewrite datatype to ts ([e0d0b5c](https://github.com/faker-js/faker/commit/e0d0b5cc45cfde3dd6a351650becf9ea83b99ae2))


### Bug Fixes

* added missing keys into package.json ([#148](https://github.com/faker-js/faker/issues/148)) ([b20f80b](https://github.com/faker-js/faker/commit/b20f80bf7886e89045e369add9c5598d3586a225))

## [6.0.0-alpha.2](https://github.com/faker-js/faker/compare/v6.0.0-alpha.1...v6.0.0-alpha.2) (2022-01-14)

## 6.0.0-alpha.1 (2022-01-14)

## 6.0.0-alpha.0 (2022-01-07)

For changes prior to version v6.0.0-alpha.1, see [the old changelog](./CHANGELOG_old.md).

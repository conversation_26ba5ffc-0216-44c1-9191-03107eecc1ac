/* Post Cards Grid Layout */
.post-cards-grid {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: normal;
  align-items: normal;
  align-content: normal;
    margin-top: 100px;
    margin-bottom: 100px;
}

.post-card {
  display: block;
  flex-grow: 0;
  flex-shrink: 1;
  flex-basis: auto;
  align-self: auto;
  order: 0;
}

/* Card Image */
.post-card-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: var(--color-lightgrey);
}

.post-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

/* Card Content */
.post-card-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Card Title */
.post-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.3;
    margin: 0 0 0.75rem 0;
    color: var(--color-darkgrey);
}

.post-card:hover .post-card-title {
    color: var(--ghost-accent-color, #15171a);
}

/* Ensure minimum 3 columns on larger screens */
@media (min-width: 1200px) {
    .post-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (min-width: 1400px) {
    .post-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}
